image: node:18

stages:
  - build
  - deploy

before_script:
  - npm install

build_project:
  stage: build
  script:
    - npm run build
  artifacts:
    paths:
      - out/
  except:
    changes:
      - ".gitlab-ci.yml"
      - "README.md"
  only:
    - develop
  tags:
    - git-cloud

# Deploy for develop branch (uat-drycreek.myvethub.com)
deploy_develop:
  stage: deploy
  script:
    - npx wrangler pages deploy ./out --project-name="$UAT_DRYCREEK_CLOUDFLARE_PROJECT_NAME" --branch="$CI_COMMIT_BRANCH"
  only:
    - develop
  environment:
    name: production-uat-drycreek
    url: https://uat-drycreek.myvethub.com
  variables:
    CLOUDFLARE_API_TOKEN: $VMC_CLOUDFLARE_API_TOKEN
  tags:
    - git-cloud