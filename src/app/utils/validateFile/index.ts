import { useNotificationContext } from "@/app/context/NotificationContext";

// Create a custom hook for file validation
export const useFileValidation = () => {
  const { notify } = useNotificationContext();

  // Function to validate file size
  const validateFileSize = (
    file: File,
    minSize = 1024 * 10,
    maxSize = 1024 * 1024 * 2
  ): boolean => {
    if (file.type?.startsWith("image/") && file.size < minSize) {
      notify(
        `Selected file is too small. Minimum size is ${minSize / 1024} KB.`,
        "error"
      );
      return false;
    }
    if (file.size > maxSize) {
      notify(
        `Selected file is too large. Maximum size is ${
          maxSize / (1024 * 1024)
        } MB.`,
        "error"
      );
      return false;
    }
    return true;
  };

  // Function to validate file type
  const allowedFileTypes = [
    "image/png",
    "image/jpeg",
    "image/jpg",
    "image/webp",
    "text/plain",
    "application/pdf",
    "application/msword",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  ];

  // const allowedFileTypeDescriptions = [
  //   "PNG Image",
  //   "JPEG Image",
  //   "JPG Image",
  //   "WebP Image",
  //   "Text File",
  //   "PDF Document",
  //   "Word Document",
  //   "Word Document (OpenXML)",
  //   "Excel Spreadsheet",
  //   "Excel Spreadsheet (Legacy)",
  // ];

  const allowedFileExtensions = [
    ".png",
    ".jpeg",
    ".jpg",
    ".webp",
    ".txt",
    "text",
    ".pdf",
    ".doc",
    ".docx",
    ".xlsx",
  ];

  const validateFileType = (file: File): boolean => {
    // Check if the file type is allowed
    if (allowedFileTypes?.includes(file?.type)) {
      return true;
    }

    // Fallback: Check the file extension if type is empty
    const fileExtension = file?.name
      .toLowerCase()
      .substring(file?.name?.lastIndexOf("."));

    if (allowedFileExtensions?.includes(fileExtension)) {
      return true;
    }

    // If neither type nor extension matches, notify the user
    notify(
      `"${
        file?.name
      }" has invalid file type. Only the following types are allowed: ${allowedFileExtensions?.join(
        ", "
      )}.`,
      "error"
    );

    return false;
  };

  return { validateFileSize, validateFileType };
};

// to apitalize
export const toCapitalize = (name: string): string => {
  return name
    .split(" ") // Split the name by spaces
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()) // Capitalize the first letter of each word
    .join(" "); // Join the words back together
};
