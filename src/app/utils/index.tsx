
import path from "path";
import fs from "fs/promises"; // Use promises version


export interface BlogEntry {
  ogImage: string;
  schemaHeading: string;
  ogImageAlt: string;
  ogImageUrl: string;
  keyword: string;
  metaDescription: string;
  metaTitle: string;
  id: number;
  slug: string;
  title: string;
  subtitle: string;
  content: string;
  image: string;
  bannerTitle:string;
  bannerImageUrl:string;
  sections?:unknown[];
}

// Async function to read and parse blogs.json

export const getBlogData = async (): Promise<BlogEntry[]> => {
  const filePath = path.join(
    process.cwd(),
    "src",
    "app",
    "constant",
    "blogs.json"
  );
  const jsonData = await fs.readFile(filePath, "utf-8");
  return JSON.parse(jsonData);
};
