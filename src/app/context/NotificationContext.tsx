"use client";

import Notification from "@/app/components/toaster";
import React, {
  createContext,
  useContext,
  useEffect,
  useRef,
  useState,
} from "react";

interface NotificationContextType {
  notify: (
    message: string,
    variant: "success" | "error" | "warning",
    duration?: number
  ) => void;
}

const NotificationContext = createContext<NotificationContextType | undefined>(
  undefined
);

export const useNotificationContext = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error(
      "useNotificationContext must be used within a NotificationProvider"
    );
  }
  return context;
};

export const NotificationProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [notification, setNotification] = useState<{
    message: string;
    variant: "success" | "error" | "warning";
    duration?: number;
  } | null>(null);

  const timerRef = useRef<NodeJS.Timeout | null>(null);

  const startAutoCloseTimer = (duration: number) => {
    clearAutoCloseTimer(); // Clear any existing timer
    timerRef.current = setTimeout(() => {
      setNotification(null);
    }, duration);
  };

  const clearAutoCloseTimer = () => {
    if (timerRef.current) {
      clearTimeout(timerRef.current);
      timerRef.current = null;
    }
  };

  useEffect(() => {
    if (notification && notification?.duration) {
      startAutoCloseTimer(notification.duration);
    }
    return () => clearAutoCloseTimer(); // Cleanup on unmount or notification change
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [notification]);

  const notify = (
    message: string,
    variant: "success" | "error" | "warning",
    duration: number = 2000
  ) => {
    setNotification({ message, variant, duration });
  };

  return (
    <NotificationContext.Provider value={{ notify }}>
      {children}
      {notification && (
        <Notification
          message={notification.message}
          variant={notification.variant}
          duration={notification.duration}
          onClose={() => setNotification(null)}
          onMouseEnter={clearAutoCloseTimer} // Pause timer on hover
          onMouseLeave={() => startAutoCloseTimer(notification.duration!)} // Resume timer on leave
        />
      )}
    </NotificationContext.Provider>
  );
};
