import { MenuItem } from "../components/header/menu";
import { ServiceMenutype } from "../models/common.model";

export const aboutDropDownMenuItem: ServiceMenutype[] = [
  { label: "Our Team", path: "/team-veterinary" },
  { label: "Photo Gallery", path: "/photo-gallery" },
  {
    label: "FAQS",
    path: "/faqs",
  },
];

export const servicesDropDownMenuItemMobile: ServiceMenutype[] = [
  {
    label: "Pet Aftercare",
    path: "/services/aftercare",
  },
  {
    label: "Pet Dental Care",
    path: "/services/dental-care",
  },
  {
    label: "Pet Emergencies",
    path: "/services/emergencies",
  },
  {
    label: "Pet Laboratory",
    path: "/services/laboratory",
  },
  {
    label: "Pet Medical Facilities",
    path: "/services/medical-facilities",
  },
  {
    label: "Pet Radiology",
    path: "/services/radiology",
  },
  {
    label: "Pet Soft Tissue Surgeries",
    path: "/services/soft-tissue-surgeries",
  },
  {
    label: "Pet Surgery",
    path: "/services/surgery",
  },
  {
    label: "Pet Walk-Ins",
    path: "/services/walk-ins",
  },
];

export const resoucesDropDownMenuItem: ServiceMenutype[] = [
  {
    label: "Blogs",
    path: "/blogs",
  },
  {
    label: "Online Forms",
    path: "",
    children: [
      {
        label: "New Client Form",
        path: "/online-forms/new-client-form",
      },
      {
        label: "Consent Form",
        path: "/online-forms/consent-form",
      },
    ],
  },
  {
    label: "Payment Options",
    path: "/payment-option",
  },
  {
    label: "Helpfull Links",
    path: "/helpful-links",
  },
];

export const menuItems: MenuItem[] = [
  {
    label: "Home",
    path: "/",
  },
  {
    label: "About Us",
    path: "/about-dry-creek-veterinary-hospital",
    children: [
      { label: "Our Team", path: "/team-veterinary" },
      { label: "Photo Gallery", path: "/photo-gallery" },
      {
        label: "FAQS",
        path: "/faqs",
      },
    ],
  },
  {
    label: "Services",
    children: [
      {
        label: "Pet Aftercare",
        path: "/services/aftercare",
      },
      {
        label: "Pet Dental Care",
        path: "/services/dental-care",
      },
      {
        label: "Pet Emergencies",
        path: "/services/emergencies",
      },
      {
        label: "Pet Laboratory",
        path: "/services/laboratory",
      },
      {
        label: "Pet Medical Facilities",
        path: "/services/medical-facilities",
      },
      {
        label: "Pet Radiology",
        path: "/services/radiology",
      },
      {
        label: "Pet Soft Tissue Surgeries",
        path: "/services/soft-tissue-surgeries",
      },
      {
        label: "Pet Surgery",
        path: "/services/surgery",
      },
      {
        label: "Pet Walk-Ins",
        path: "/services/walk-ins",
      },
    ],
  },
  {
    label: "Resources",
    children: [
      {
        label: "Blogs",
        path: "/blogs",
      },
      {
        label: "Online Forms",
        path: "",
        children: [
          {
            label: "New Client Form",
            path: "/online-forms/new-client-form",
          },
          {
            label: "Consent Form",
            path: "/online-forms/consent-form",
          },
        ],
      },
      {
        label: "Payment Options",
        path: "/payment-option",
      },
      {
        label: "Helpfull Links",
        path: "/helpful-links",
      },
    ],
  },
  {
    label: "Online Pharmacy",
    path: "https://drycreekveterinaryhospital.securevetsource.com/site/view/site/view/HomeDelivery.pml?retUrl=https://www.drycreekvet.com&cms=",
    hasRedirectUrl: true,
  },
  {
    label: "Contact",
    path: "/contact-us",
  },
  // {
  //   label: "Appointment",
  //   path: "/contact-us",
  //   isButton: true,
  // },
];

export const emailValidationRule = {
  pattern: {
    value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    message: "Invalid email format",
  },
};

export const mobileValidationRule = {
  pattern: {
    value: /^[0-9]{10}$/, // matches exactly 10 digits
    message: "Enter valid mobile number",
  },
};

export const zipCodeValidationRule = {
  required: "Required",
  pattern: {
    value: /^\d{5}(-\d{4})?$/,
    message: "Valid ZIP (e.g. 12345 or 12345-6789)",
  },
};

export type TimezoneMapType = {
  [key: string]: string;
};

export const timezoneMap: TimezoneMapType = {
  ACDT: "Australia/Adelaide",
  ACST: "Australia/Darwin",
  ACT: "America/Rio_Branco",
  ADT: "America/Halifax",
  AEDT: "Australia/Sydney",
  AEST: "Australia/Brisbane",
  AFT: "Asia/Kabul",
  AKDT: "America/Anchorage",
  AKST: "America/Anchorage",
  ALMT: "Asia/Almaty",
  AMST: "America/Boa_Vista",
  AMT: "America/Boa_Vista",
  ANAST: "Asia/Anadyr",
  ANAT: "Asia/Anadyr",
  AQTT: "Asia/Aqtau",
  ART: "America/Argentina/Buenos_Aires",
  AST: "America/Puerto_Rico",
  AWST: "Australia/Perth",
  AZOST: "Atlantic/Azores",
  AZOT: "Atlantic/Azores",
  AZT: "Asia/Baku",
  BDT: "Asia/Dhaka",
  BIOT: "Indian/Chagos",
  BIT: "Pacific/Pago_Pago",
  BOT: "America/La_Paz",
  BRST: "America/Sao_Paulo",
  BRT: "America/Sao_Paulo",
  BST: "Europe/London",
  BTT: "Asia/Thimphu",
  CAT: "Africa/Harare",
  CCT: "Indian/Cocos",
  CDT: "America/Chicago",
  CEST: "Europe/Paris",
  CET: "Europe/Paris",
  CHADT: "Pacific/Chatham",
  CHAST: "Pacific/Chatham",
  CHOT: "Asia/Choibalsan",
  CHUT: "Pacific/Chuuk",
  CKT: "Pacific/Rarotonga",
  CLST: "America/Santiago",
  CLT: "America/Santiago",
  COST: "America/Bogota",
  COT: "America/Bogota",
  CST: "America/Chicago",
  CT: "America/Chicago",
  CVT: "Atlantic/Cape_Verde",
  CWST: "Australia/Eucla",
  CXT: "Indian/Christmas",
  DAVT: "Antarctica/Davis",
  EASST: "Pacific/Easter",
  EAST: "Pacific/Easter",
  EAT: "Africa/Nairobi",
  ECT: "America/Guayaquil",
  EDT: "America/New_York",
  EEST: "Europe/Istanbul",
  EET: "Europe/Istanbul",
  EGST: "Atlantic/Jan_Mayen",
  EGT: "Atlantic/Jan_Mayen",
  EST: "America/New_York",
  FET: "Europe/Kaliningrad",
  FJT: "Pacific/Fiji",
  FKST: "Atlantic/Stanley",
  FKT: "Atlantic/Stanley",
  FNT: "America/Noronha",
  GALT: "Pacific/Galapagos",
  GAMT: "Pacific/Gambier",
  GET: "Asia/Tbilisi",
  GFT: "America/Cayenne",
  GILT: "Pacific/Tarawa",
  GMT: "Etc/GMT",
  GST: "Asia/Dubai",
  GYT: "America/Guyana",
  HDT: "Pacific/Honolulu",
  HAEC: "Europe/Paris",
  HST: "Pacific/Honolulu",
  HKT: "Asia/Hong_Kong",
  HMT: "Asia/Kolkata",
  HOVT: "Asia/Hovd",
  ICT: "Asia/Bangkok",
  IDT: "Asia/Jerusalem",
  IOT: "Indian/Chagos",
  IRDT: "Asia/Tehran",
  IRST: "Asia/Tehran",
  IST: "Asia/Kolkata",
  JST: "Asia/Tokyo",
  KGT: "Asia/Bishkek",
  KOST: "Pacific/Kosrae",
  KRAT: "Asia/Krasnoyarsk",
  KST: "Asia/Seoul",
  LHST: "Australia/Lord_Howe",
  LINT: "Pacific/Kiritimati",
  MAGT: "Asia/Magadan",
  MART: "Pacific/Marquesas",
  MAWT: "Antarctica/Mawson",
  MDT: "America/Denver",
  MHT: "Pacific/Majuro",
  MIST: "Antarctica/Macquarie",
  MIT: "Pacific/Apia",
  MMT: "Asia/Yangon",
  MSK: "Europe/Moscow",
  MST: "America/Denver",
  MUT: "Indian/Mauritius",
  MVT: "Indian/Maldives",
  MYT: "Asia/Kuala_Lumpur",
  NCT: "Pacific/Noumea",
  NDT: "America/St_Johns",
  NFT: "Pacific/Norfolk",
  NOVT: "Asia/Novosibirsk",
  NPT: "Asia/Kathmandu",
  NST: "America/St_Johns",
  NT: "America/St_Johns",
  NUT: "Pacific/Niue",
  NZDT: "Pacific/Auckland",
  NZST: "Pacific/Auckland",
  OMST: "Asia/Omsk",
  ORAT: "Asia/Oral",
  PDT: "America/Los_Angeles",
  PET: "America/Lima",
  PETT: "Asia/Kamchatka",
  PGT: "Pacific/Port_Moresby",
  PHOT: "Pacific/Enderbury",
  PHT: "Asia/Manila",
  PKT: "Asia/Karachi",
  PMDT: "America/Miquelon",
  PMST: "America/Miquelon",
  PONT: "Pacific/Pohnpei",
  PST: "America/Los_Angeles",
  PWT: "Pacific/Palau",
  PYST: "America/Asuncion",
  PYT: "America/Asuncion",
  RET: "Indian/Reunion",
  ROTT: "Antarctica/Rothera",
  SAKT: "Asia/Sakhalin",
  SAMT: "Europe/Samara",
  SAST: "Africa/Johannesburg",
  SBT: "Pacific/Guadalcanal",
  SCT: "Indian/Mahe",
  SGT: "Asia/Singapore",
  SRET: "Asia/Srednekolymsk",
  SRT: "America/Paramaribo",
  SST: "Pacific/Midway",
  SYOT: "Antarctica/Syowa",
  TAHT: "Pacific/Tahiti",
  TFT: "Indian/Kerguelen",
  TJT: "Asia/Dushanbe",
  TKT: "Pacific/Fakaofo",
  TLT: "Asia/Dili",
  TMT: "Asia/Ashgabat",
  TOT: "Pacific/Tongatapu",
  TVT: "Pacific/Funafuti",
  UCT: "Etc/UTC",
  ULAT: "Asia/Ulaanbaatar",
  UTC: "Etc/UTC",
  UYST: "America/Montevideo",
  UYT: "America/Montevideo",
  VET: "America/Caracas",
  VLAT: "Asia/Vladivostok",
  VOLT: "Europe/Volgograd",
  VOST: "Antarctica/Vostok",
  VUT: "Pacific/Efate",
  WAKT: "Pacific/Wake",
  WAST: "Africa/Windhoek",
  WAT: "Africa/Lagos",
  WEDT: "Europe/Lisbon",
  WEST: "Europe/Lisbon",
  WET: "Europe/Lisbon",
  WST: "Australia/Perth",
  YAKT: "Asia/Yakutsk",
  YEKT: "Asia/Yekaterinburg",
};

export const countryPhoneCodes = [
  { name: "United States", iso2: "US", dialCode: "+1" },
  { name: "India", iso2: "IN", dialCode: "+91" },
  { name: "United Kingdom", iso2: "GB", dialCode: "+44" },
  // { "name": "Canada", "iso2": "CA", "dialCode": "+1" },
  { name: "Australia", iso2: "AU", dialCode: "+61" },
  { name: "China", iso2: "CN", dialCode: "+86" },
  { name: "Indonesia", iso2: "ID", dialCode: "+62" },
];
