

// utils/getDynamicConfig.ts
export const getDynamicConfig = () => {
 const hostEnvironement = process.env.NEXT_PUBLIC_API_BASE!; 

  if (typeof window === "undefined") {
    // SSG or SSR build time – return default values
    return {
      apiBaseUrl: "https://uat-api.myvethub.com",
      baseUrl: hostEnvironement,
    };
  }

  const hostname = window.location.hostname?.toLowerCase();

  if (
    [
      "drycreekvet.com",
      "www.drycreekvet.com",
    ].includes(hostname)
  ) {
    return {
      baseUrl: hostEnvironement,
      apiBaseUrl: "https://api.myvethub.com",
    };
  } 


  // Fallback to QA/Dev
  return {
    baseUrl: hostEnvironement,
    apiBaseUrl: "https://uat-api.myvethub.com",
  };
};
