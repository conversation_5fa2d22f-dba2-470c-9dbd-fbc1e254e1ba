export const API_CONSTANTS = {
  auth: {
    login: `/api/user/sign-in`,
  },
  uploadDetails: {
    save: `/api/appointment/uploadFiles`,
    // get: (id: string | null) => `/api/user/upload-details/${id}`,
    // delete: (id: string | number | null) => `/api/user/upload-details/${id}`,
    // find: `/api/user/upload-details`,
  },
  appointments: {
    get: `/api/appointment/getAll`,
    getAllAppointment: '/api/v1/appointment/by-users',
    getAppointment: '/api/v1/appointment/'
  },
  clients: {
    get: `/api/appointment/getDetailsByDate`,
  },
  documents: {
    get: "/api/appointment/getAllFiles",
    download: "/api/appointment/getFile",
    upload: "/api/appointment/uploadFilesByAdmin",
    delete: "/api/appointment/deleteFile",
  },
  sendOTP: {
    sendOTP: "/api/appointment/sendOtp",
    OTPVerificaiton: "/api/appointment/verification",
  },
  sendContactEmail: {
    contactEmail: "/api/user/contactus/submit",
  },
  notes: {
    get: "/api/appointment/notes/getAll",
    save: "/api/appointment/notes/create",
    update: "/api/appointment/notes/update",
  },
  followUp: {
    schedule: "api/appointment/followupRequest/schedule",
    updateSchedule: "api/appointment/followupRequest/updateSchedule",
  },
  users: {
    get: '/api/v1/clinic/'
  },
  clinic:{
    getProvider:'/api/v1/clinic/my-provider'
  },
  careers:{
    save:'/api/v1/clinic/careers/apply'
  },
  otp:{
    get:'/api/v1/user/send-otp',
    save:'/api/v1/user/verify-booking-otp'
  },
  bookAppointment:{
    save:'/calendar/api/v1/calendar/book-slot-ivr',
    saveWithCaptcha:'/calendar/api/v1/calendar/book-slot-ivr-with-captcha',
    checkUserAvailability:'/calendar/api/v1/calendar/check-user-availability'
  },
   payment:{
    save:'/calendar/api/v1/payments/pay-now'
  }
};
