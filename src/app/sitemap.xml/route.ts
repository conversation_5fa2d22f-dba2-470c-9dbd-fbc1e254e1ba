/** app/sitemap.xml/route.ts **/
export const dynamic = "force-static";

function getSitemap() {
  const map = [
    {
      url: "https://drycreekvet.com/",
      lastModified: new Date(),
      changeFrequency: "weekly",
      priority: 1.0,
    },
    {
      url: "https://drycreekvet.com/about-dry-creek-veterinary-hospital/",
      lastModified: new Date(),
      changeFrequency: "monthly",
      priority: 0.9,
    },
    {
      url: "https://drycreekvet.com/services/",
      lastModified: new Date(),
      changeFrequency: "monthly",
      priority: 0.9,
    },
    {
      url: "https://drycreekvet.com/team-veterinary/",
      lastModified: new Date(),
      changeFrequency: "monthly",
      priority: 0.8,
    },
    {
      url: "https://drycreekvet.com/contact-us/",
      lastModified: new Date(),
      changeFrequency: "monthly",
      priority: 0.8,
    },
    {
      url: "https://drycreekvet.com/faqs/",
      lastModified: new Date(),
      changeFrequency: "monthly",
      priority: 0.7,
    },
    {
      url: "https://drycreekvet.com/photo-gallery/",
      lastModified: new Date(),
      changeFrequency: "monthly",
      priority: 0.6,
    },
    {
      url: "https://drycreekvet.com/payment-option/",
      lastModified: new Date(),
      changeFrequency: "yearly",
      priority: 0.5,
    },
    {
      url: "https://drycreekvet.com/helpful-links/",
      lastModified: new Date(),
      changeFrequency: "yearly",
      priority: 0.4,
    },
  ];

  return `<?xml version="1.0" encoding="UTF-8"?>
    <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
        ${map
          .map(
            item => `
                <url>
                  <loc>${item.url}</loc>
                  <lastmod>${
                    item.lastModified.toISOString().split("T")[0]
                  }</lastmod>
                  <changefreq>${item.changeFrequency}</changefreq>
                  <priority>${item.priority}</priority>
                </url>
              `
          )
          .join("")}
        </urlset>
      `;
}

export async function GET() {
  return new Response(getSitemap(), {
    headers: {
      "Content-Type": "text/xml",
    },
  });
}
