import re
import smtplib
import os
import json
import base64
from io import BytesIO
from email.mime.multipart import MI<PERSON><PERSON>ultipart
from email.mime.text import MIMEText
from email.mime.application import MIMEApplication

from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Image
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.lib.pagesizes import A4
from reportlab.lib.units import inch
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.cidfonts import UnicodeCIDFont


def create_pdf(body):
    """Generate PDF in memory (runtime) and return as bytes"""
    buffer = BytesIO()
    doc = SimpleDocTemplate(buffer, pagesize=A4)
    story = []
    styles = getSampleStyleSheet()
    pdfmetrics.registerFont(UnicodeCIDFont("HeiseiMin-W3"))

    # Blog/Message Content
    message = body.get("message", "")
    story.append(Paragraph("<b>Blog Content:</b>", styles["Heading2"]))
    story.append(Spacer(1, 12))
    story.append(Paragraph(message, styles["Normal"]))
    story.append(Spacer(1, 20))

    # Signature Image
    signature_base64 = body.get("signature", "")
    if signature_base64.startswith("data:image"):
        signature_base64 = signature_base64.split(",")[1]

    if signature_base64:
        try:
            signature_data = base64.b64decode(signature_base64)
            temp_img = "/tmp/signature.png"   # Lambda writable dir
            with open(temp_img, "wb") as f:
                f.write(signature_data)

            story.append(Spacer(1, 30))
            story.append(Paragraph("<b>Signature:</b>", styles["Heading2"]))
            story.append(Spacer(1, 12))
            story.append(Image(temp_img, width=2 * inch, height=1 * inch))
        except Exception as e:
            story.append(Paragraph(f"(Error decoding signature: {e})", styles["Normal"]))

    # Build PDF into buffer
    doc.build(story)
    buffer.seek(0)
    return buffer.read()


def lambda_handler(event, context):
    headers = event.get("headers", {}) or {}
    origin = headers.get("origin") or headers.get("Origin") or "http://localhost:3000"

    try:
        body = json.loads(event["body"]) if event.get("body") else event
    except Exception as e:
        return {"statusCode": 400, "body": f"Invalid JSON: {str(e)}"}

    sender_email = os.environ["SENDER_EMAIL"]
    receiver_emails = os.environ["RECEIVER_EMAILS"].split(",")
    app_password = os.environ["APP_PASSWORD"]

    first_name = body.get("firstName", "")
    last_name = body.get("lastName", "")
    user_email = body.get("email", "")
    message = body.get("message", "")

    full_name = f"{first_name} {last_name}".strip()
    subject = "New Contact Us Message"

    html_content = f"""
    <html>
      <body style="font-family: Arial, sans-serif;">
        <h2>New Contact Us Submission</h2>
        <p><strong>Name:</strong> {full_name}</p>
        <p><strong>Email:</strong> {user_email}</p>
        <p><strong>Message:</strong></p>
        <p>{message}</p>
      </body>
    </html>
    """

    # Generate PDF bytes in memory
    pdf_bytes = create_pdf(body)

    msg = MIMEMultipart("mixed")
    msg["Subject"] = subject
    msg["From"] = sender_email
    msg["To"] = ", ".join(receiver_emails)

    # HTML body
    msg.attach(MIMEText(html_content, "html"))

    # Attach PDF from memory
    pdf_attachment = MIMEApplication(pdf_bytes, _subtype="pdf")
    pdf_attachment.add_header("Content-Disposition", "attachment", filename="submission.pdf")
    msg.attach(pdf_attachment)

    try:
        server = smtplib.SMTP("smtp.gmail.com", 587)
        server.starttls()
        server.login(sender_email, app_password)
        server.sendmail(sender_email, receiver_emails, msg.as_string())
        server.quit()
        return {"statusCode": 200, "body": "Email sent successfully with runtime PDF"}
    except Exception as e:
        return {"statusCode": 500, "body": f"Error sending email: {str(e)}"}
