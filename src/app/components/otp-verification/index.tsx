"use client";

import { API_CONSTANTS } from "@/app/constant/api-constants/apiConfig";
import { useNotificationContext } from "@/app/context/NotificationContext";
import { usePostApi } from "@/app/services/useApi";
import { useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";

interface AppointmentPayload {
  token: string;
  appointmentData: AppointmentData;
}

interface AppointmentData {
  client: Client;
  meetUrl: string;
  date: string;
  timezone: string;
  startTime: string;
  endTime: string;
  id: string;
}

interface Client {
  id: string;
  name: string;
}

interface OTPVerificationProps {
  setOtpVerificationToken: (tokenValue: AppointmentPayload) => void;
}

const OTPVerification = (props: OTPVerificationProps) => {
  const { setOtpVerificationToken } = props;
  const searchParams = useSearchParams();
  const [isModalVisible, setModalVisible] = useState(false);
  const [otp, setOtp] = useState(["", "", "", ""]); // To store OTP values
  //   const [loading, setLoading] = useState(false); // To manage button state
  const [error, setError] = useState(false); // To handle invalid OTP state
  const [step, setStep] = useState("sendOtp"); // "sendOtp" or "verifyOtp"
  const [message, setMessage] = useState("");
  const [timer, setTimer] = useState(120); // 2 minutes
  const [resendDisabled, setResendDisabled] = useState(true);
  const appointmentId = searchParams.get("appointmentId");
  // const clientEmailId = searchParams.get("email");
  const { notify } = useNotificationContext();

  const [otpResponse, setOtpResponse] = useState<{
    message: string;
    verificationId: string;
  }>(); // To store OTP response

  // api call send OTP
  const { postData: postOTPSendData, isLoading: postOTPSendLoading } =
    usePostApi("");

  // api call OTP verification
  const {
    postData: postOTPVerificationData,
    isLoading: postOTPVerificationLoading,
  } = usePostApi("");

  useEffect(() => {
    if (resendDisabled && timer > 0) {
      const countdown = setInterval(() => {
        setTimer((prev) => prev - 1);
      }, 1000);
      return () => clearInterval(countdown); // Cleanup interval
    } else if (timer === 0) {
      setResendDisabled(false);
    }
  }, [timer, resendDisabled]);

  useEffect(() => {
    if (appointmentId) {
      setModalVisible(true);
    }
  }, [appointmentId, searchParams]);

  const handleSendOtp = async () => {
    setMessage("");
    try {
      const response = await postOTPSendData(
        {
          appointmentId: appointmentId,
        },
        API_CONSTANTS?.sendOTP?.sendOTP
      );

      if (response?.status === 200) {
        setOtpResponse(response?.data?.body);
        setError(false);
        setOtp(["", "", "", ""]);
        setStep("verifyOtp");
        notify(response?.data?.message, "success");
        setTimer(120); // Reset the timer to 2 minutes
        setResendDisabled(true); // Disable resend link
      } else {
        notify(response?.response?.data?.message, "error");
      }
    } catch {
      setMessage("An error occurred. Please try again later.");
    }
  };

  const handleInputChange = (value: string, index: number) => {
    if (!/^\d*$/.test(value)) return; // Allow only numeric input
    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);

    // Automatically focus next input
    if (value && index < otp?.length - 1) {
      const nextInput = document.getElementById(`otp-input-${index + 1}`);
      nextInput?.focus();
    }

    // Reset error state on change
    if (error) setError(false);
  };

  const handleVerify = async () => {
    const otpValue = otp.join("");
    if (otpValue?.length < 4) {
      setError(true);
      return;
    }

    try {
      setError(false); // Clear previous error state

      //   // Replace with your API endpoint
      const response = await postOTPVerificationData(
        {
          appointmentId: appointmentId,
          otp: otpValue,
          verificationId: otpResponse?.verificationId,
        },
        API_CONSTANTS?.sendOTP?.OTPVerificaiton
      );

      if (response?.status === 200) {
        setModalVisible(false);
        setOtpVerificationToken(response?.data?.body);
        notify(response?.data?.message, "success");
      } else {
        setError(true); // Show error for invalid OTP
        notify(response?.response?.data?.message, "error");
      }
    } catch {
      setMessage("An error occurred. Please try again later.");
      setError(true);
    } finally {
      //   setLoading(false);
    }
  };

  const formatTimer = () => {
    const minutes = Math.floor(timer / 60);
    const seconds = timer % 60;
    return `${minutes}:${seconds.toString().padStart(2, "0")}`;
  };

  if (!isModalVisible) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-[#000000b3] bg-opacity-50">
      <div className="relative bg-white px-6 pt-10 pb-9 shadow-xl mx-auto w-full max-w-lg rounded-2xl">
        <div className="mx-auto flex w-full max-w-md flex-col space-y-16">
          {step === "sendOtp" ? (
            <>
              {/* Send OTP Screen */}
              <div className="flex flex-col items-center justify-center text-center space-y-4">
                <div className="font-semibold text-3xl md:text-4xl">
                  <p className="font-dancing text-primary">Get one time code</p>
                </div>
                <div className="text-sm font-medium text-gray-500">
                  <p>
                    Get one time code to your <strong>Email / Mobile</strong> to verify
                    your access
                  </p>
                </div>
              </div>
              <button
                onClick={handleSendOtp}
                disabled={postOTPSendLoading}
                className={`flex flex-row items-center justify-center text-center w-full border rounded-xl outline-none py-5 ${postOTPSendLoading ? "bg-gray-400" : "bg-[#0075D4]"
                  } text-white text-sm shadow-sm`}
              >
                {postOTPSendLoading ? "Sending..." : "Send"}
              </button>
              {message && (
                <p className="text-center text-sm text-gray-500">{message}</p>
              )}
            </>
          ) : (
            <>
              {/* Verify OTP Screen */}
              <div className="flex flex-col items-center justify-center text-center space-y-4">
                <div className="font-semibold text-3xl md:text-4xl">
                  <p className="font-dancing text-primary">
                    One Time Code Verification
                  </p>
                </div>
                <div className="text-sm font-medium text-gray-400">
                  <p>
                    <strong>{otpResponse?.message}</strong>
                  </p>
                </div>
              </div>
              <form onSubmit={(e) => e.preventDefault()}>
                <div className="flex flex-col space-y-8">
                  {/* OTP Input Fields */}
                  <div className="flex flex-row items-center justify-between mx-auto w-full max-w-xs">
                    {otp.map((item, idx) => (
                      <div key={idx} className="w-16 h-16">
                        <input
                          id={`otp-input-${idx}`}
                          className={`w-full h-full flex flex-col items-center justify-center text-center font-semibold px-5 outline-none rounded-xl border text-lg bg-white focus:bg-gray-50 focus:ring-2 ${error
                              ? "border-red-500 ring-red-500"
                              : "border-gray-200 ring-[#0075D4]"
                            }`}
                          type="text"
                          maxLength={1}
                          value={item}
                          autoComplete="off"
                          onChange={(e) =>
                            handleInputChange(e.target.value, idx)
                          }
                        />
                      </div>
                    ))}
                  </div>

                  {/* Validation Message */}
                  {error && (
                    <p className="text-red-500 text-center text-sm font-medium">
                      Invalid Code. Please try again.
                    </p>
                  )}

                  {/* Buttons */}
                  <div className="flex flex-col space-y-5">
                    <div>
                      <button
                        type="button"
                        onClick={handleVerify}
                        disabled={postOTPVerificationLoading}
                        className={`flex flex-row items-center justify-center text-center w-full border rounded-xl outline-none py-5 ${postOTPVerificationLoading
                            ? "bg-gray-400"
                            : "bg-[#0075D4]"
                          } text-white text-sm shadow-sm`}
                      >
                        {postOTPVerificationLoading ? "Verifying..." : "Verify"}
                      </button>
                    </div>

                    <div className="flex flex-row items-center justify-center text-center text-sm font-medium space-x-1 text-gray-500">
                      <p>Didn&apos;t receive code?</p>{" "}
                      <button
                        onClick={handleSendOtp}
                        disabled={resendDisabled || postOTPSendLoading}
                        className={`text-[#0075D4] ${resendDisabled ? "cursor-not-allowed opacity-50" : ""
                          }`}
                      >
                        Resend {resendDisabled && `(${formatTimer()})`}
                      </button>
                    </div>
                  </div>
                </div>
              </form>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default OTPVerification;
