import { <PERSON>ert<PERSON><PERSON>cle, <PERSON>ert<PERSON><PERSON>gle, CheckCircle, X } from "lucide-react";
import React from "react";

interface NotificationProps {
  message: string;
  variant: "success" | "error" | "warning";
  duration?: number;
  onClose: () => void; // Handler to close the notification
  onMouseEnter: () => void;
  onMouseLeave: () => void;
}

const Notification: React.FC<NotificationProps> = ({
  message,
  variant,
  onClose,
  onMouseEnter,
  onMouseLeave,
}) => {
  const variantStyles = {
    success: "bg-green-500 text-white",
    error: "bg-red-500 text-white",
    warning: "bg-yellow-500 text-black",
  };

  const variantIcons = {
    success: <CheckCircle size={20} className="mr-2" />,
    error: <AlertCircle size={20} className="mr-2" />,
    warning: <AlertTriangle size={20} className="mr-2" />,
  };

  return (
    <div
      className={`fixed top-2 md:top-4 right-1 md:right-4 p-4 min-w-56 md:w-[450px] rounded shadow z-[1050] flex items-center justify-between ${variantStyles[variant]}`}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
    >
      <div className="flex items-center space-x-2 flex-grow">
        {/* Icon and message container */}
        <div className="flex-shrink-0">{variantIcons[variant]}</div>
        <div className="flex-1 overflow-hidden text-ellipsis whitespace-normal">
          <span>{message}</span>
        </div>
      </div>
      <button
        onClick={onClose}
        className="ml-4 text-xl font-bold text-current hover:text-opacity-75"
        aria-label="Close notification"
      >
        <X size={20} />
      </button>
    </div>
  );
};

export default Notification;
