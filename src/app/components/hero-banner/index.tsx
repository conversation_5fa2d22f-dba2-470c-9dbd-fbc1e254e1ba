import { ReactNode } from "react";
interface HeroBannerProps {
  heading?: ReactNode;
  description?: ReactNode;
  icon?: ReactNode;
  buttonLabel?: string;
  redirectPath?: string;
  img?: string;
  onClick?: () => void;
}

const HeroBanner = ({ heading, description, img }: HeroBannerProps) => {
  return (
    <div className="relative w-full  py-24">
      <div
        className="absolute inset-0 bg-cover bg-center"
        style={{
          backgroundImage: `url(${img || "/images/blog-image-4.jpg"})`,
        }}
      ></div>
      <div className="absolute inset-0 bg-black/20 backdrop-blur-sm"></div>

      <div className="relative z-10 flex flex-col items-center justify-center text-center  ">
        <div className="bg-gradient-to-b from-white/50 to-white/70 rounded-md container mx-auto px-6 py-10">
          {heading && (
            <h2 className="text-2xl md:text-4xl font-bold text-gray-900 mb-2">
              {heading}
            </h2>
          )}
          {description && (
            <p className=" text-black max-w-2xl m-auto">{description}</p>
          )}
        </div>
      </div>
    </div>
  );
};

export default HeroBanner;
