import { ButtonPropsTypes } from "@/app/models/common.model";
import Link from "next/link";
import { memo } from "react";

const LinkButton = (props: ButtonPropsTypes) => {
  const { label, className, navigateLink, target, onClick } = props;
  return (
    <Link
      href={navigateLink || ""}
      className={`${className} bg-primary text-white px-6 py-3 rounded-full text-sm md:text-base group `}
      aria-label={`${label}` || "Button"}
      target={target}
      onClick={onClick}
    >
      <span className="font-medium">
        {label} <span className="sr-only">about {navigateLink}</span>
      </span>
    </Link>
  );
};
export default memo(LinkButton);
