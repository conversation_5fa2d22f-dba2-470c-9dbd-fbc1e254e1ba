// app/blogs/pet-vaccinations/page.tsx
import { ArrowLeft } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

export default function PetVaccinationsBlog() {
  return (
    <section className="bg-white text-black py-5 md:py-14 px-4">
      <div className="container mx-auto">
        {/* Breadcrumb */}
        <nav className="mb-8 text-sm text-gray-500">
          <Link href="/blogs" className="hover:text-blue-600">
            Blog
          </Link>{" "}
          ›{" "}
          <span className="text-gray-800 font-medium">
            Pet Vaccinations – Building a Shield Against Disease
          </span>
        </nav>

        {/* Title */}
        <h1 className="text-4xl font-bold text-gray-900 mb-6 leading-snug">
          Pet Vaccinations – Building a Shield Against Disease
        </h1>

        {/* Hero Image */}
        <div className="mb-10">
          <Image
            src="/images/blogs/pet-vaccinations.jpg"
            alt="Veterinarian vaccinating a puppy"
            width={1280}
            height={720}
            className="rounded-2xl shadow-md"
          />
        </div>

        {/* Blog Content */}
        <article className="prose prose-lg max-w-none text-gray-800">
          <p className="text-gray-700 mb-6">
            Vaccines protect pets from serious illnesses, keeping them safe and
            their communities healthy. At{" "}
            <span className="font-bold text-gray-900">
              Dry Creek Veterinary Hospital
            </span>
            , vaccinations are a cornerstone of preventive care.
          </p>

          <h2 className="text-2xl font-bold text-gray-900 mt-8 mb-4">
            Core Vaccines
          </h2>
          <ul className="space-y-3 text-gray-700 pl-5 mb-4">
            <li className="flex items-start">
              <span className="text-blue-600 mr-3 mt-1">•</span>
              <span>
                <strong className="font-bold text-gray-900">Dogs</strong>{" "}
                Rabies, distemper, parvovirus, hepatitis
              </span>
            </li>
            <li className="flex items-start">
              <span className="text-blue-600 mr-3 mt-1">•</span>
              <span>
                <strong className="font-bold text-gray-900">Cats</strong>{" "}
                Rabies, FVRCP (feline viral rhinotracheitis, calicivirus,
                panleukopenia)
              </span>
            </li>
          </ul>

          <h2 className="text-2xl font-bold text-gray-900 mt-8 mb-4">
            Why Vaccines Matter
          </h2>
          <p className="text-gray-700 mb-6">
            Vaccines prevent highly contagious, life-threatening diseases. They
            also reduce the spread of infections to other animals, promoting a
            healthier community.
          </p>

          <h2 className="text-2xl font-bold text-gray-900 mt-8 mb-4">
            Tailored Vaccination Plans
          </h2>
          <p className="text-gray-700 mb-6">
            Dry Creek provides individualized schedules based on age, lifestyle,
            travel, and risk factors. Optional vaccines may be recommended for
            boarding, outdoor activity, or specific health risks.
          </p>

          <p className="text-lg  bg-blue-50 p-4 rounded-lg">
            <span className="font-semibold text-gray-900">Conclusion : </span>
            <span>
              Vaccinations are essential to lifelong wellness. Regular
              immunizations ensure your pet is protected from preventable
              diseases, keeping them happy and healthy.
            </span>
            <br />
            <br />
            <span className="font-semibold text-gray-900">Keywords : </span>
            <span>
              Pet vaccinations Dry Creek, dog and cat vaccines Sacramento,
              preventive pet care, immunization schedule pets, veterinary
              vaccine clinic.
            </span>
          </p>
        </article>

        {/* Back link */}
        <div className="mt-10">
          <Link
            href="/blogs"
            className="text-blue-600 hover:underline flex items-center gap-3"
          >
            <ArrowLeft className="h-5 w-5" /> Back to all articles
          </Link>
        </div>
      </div>
    </section>
  );
}
