// app/blogs/puppy-kitten-care/page.tsx
import { ArrowLeft } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

export default function PuppyKittenCareBlog() {
  return (
    <section className="bg-white text-black py-5 md:py-14 px-4">
      <div className="container mx-auto">
        {/* Breadcrumb */}
        <nav className="mb-8 text-sm text-gray-500">
          <Link href="/blogs" className="hover:text-blue-600">
            Blog
          </Link>{" "}
          ›{" "}
          <span className="text-gray-800 font-medium">
            P<PERSON>py and Kitten Care – Setting the Right Start
          </span>
        </nav>

        {/* Title */}
        <h1 className="text-4xl font-bold text-gray-900 mb-6 leading-snug">
          Puppy and Kitten Care – Setting the Right Start
        </h1>

        {/* Hero Image */}
        <div className="mb-10">
          <Image
            src="/images/blogs/puppy-kitten-care.jpg"
            alt="Happy puppy and kitten at a veterinary checkup"
            width={1280}
            height={720}
            className="rounded-2xl shadow-md"
          />
        </div>

        {/* Blog Content */}
        <article className="prose prose-lg max-w-none text-gray-800">
          <p className="text-gray-700 mb-6">
            Welcoming a new puppy or kitten is exciting, but it also requires
            careful planning for their health. At{" "}
            <span className="font-bold text-gray-900">
              Dry Creek Veterinary Hospital
            </span>
            , we provide guidance to ensure young pets grow into strong, healthy
            adults.
          </p>

          <h2 className="text-2xl font-bold text-gray-900 mt-8 mb-4">
            Early Veterinary Care
          </h2>
          <ul className="space-y-3 text-gray-700 pl-5 mb-4">
            <li className="flex items-start">
              <span className="text-blue-600 mr-3 mt-1">•</span>
              <span>Initial wellness exams</span>
            </li>
            <li className="flex items-start">
              <span className="text-blue-600 mr-3 mt-1">•</span>
              <span>Vaccination schedules</span>
            </li>
            <li className="flex items-start">
              <span className="text-blue-600 mr-3 mt-1">•</span>
              <span>Parasite prevention</span>
            </li>
            <li className="flex items-start">
              <span className="text-blue-600 mr-3 mt-1">•</span>
              <span>Spaying/neutering discussions</span>
            </li>
          </ul>

          <h2 className="text-2xl font-bold text-gray-900 mt-8 mb-4">
            Nutrition and Growth
          </h2>
          <p className="text-gray-700 mb-6">
            Age-appropriate diets support development, muscle growth, and immune
            system strength. Portion control and monitoring weight are critical.
          </p>

          <h2 className="text-2xl font-bold text-gray-900 mt-8 mb-4">
            Behavior and Socialization
          </h2>
          <p className="text-gray-700 mb-6">
            Early socialization, positive reinforcement training, and
            environmental enrichment shape confident, well-adjusted adult pets.
          </p>

          <p className="text-lg bg-blue-50 p-4 rounded-lg">
            <span className="font-semibold text-gray-900">Conclusion : </span>
            <span>
              Proper care during the early months sets the foundation for
              lifelong health. Dry Creek Veterinary Hospital partners with you
              to ensure your new companion thrives.
            </span>
            <br />
            <br />
            <span className="font-semibold text-gray-900">Keywords : </span>
            <span>
              Puppy care, Dry Creek, kitten wellness Sacramento, early pet
              health, vaccinations for young pets, nutrition, and socialization
              of pets.
            </span>
          </p>
        </article>

        {/* Back link */}
        <div className="mt-10">
          <Link
            href="/blogs"
            className="text-blue-600 hover:underline flex items-center gap-3"
          >
            <ArrowLeft className="h-5 w-5" /> Back to all articles
          </Link>
        </div>
      </div>
    </section>
  );
}
