import { ChevronDown } from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useState } from "react";

export interface MenuItem {
  label: string;
  path?: string;
  isButton?: boolean;
  hasRedirectUrl?: boolean;
  children?: MenuItem[];
}
interface NavMenuProps {
  items?: MenuItem[];
  depth?: number;
}

const NavMenu = ({ items, depth = 0 }: NavMenuProps) => {
  const pathName = usePathname();
  const [openIndex, setOpenIndex] = useState<number | null>(null);

  const handleMouseEnter = (index: number) => {
    setOpenIndex(index);
  };

  const handleMouseLeave = () => {
    setOpenIndex(null);
  };

  return (
    <ul
      className={`${
        depth === 0
          ? "flex space-x-2 items-center"
          : "absolute left-full top-0 w-56 px-2 mt-[2px] bg-white shadow-lg rounded-md z-50"
      }`}
    >
      {items?.map((item, index) => {
        const hasChildren = item.children && item.children.length > 0;
        const isOpen = openIndex === index;

        return (
          <li
            key={`${item.path}-${index}`}
            onMouseEnter={() => handleMouseEnter(index)}
            onMouseLeave={handleMouseLeave}
            className={`${depth === 0 ? "relative" : "relative group"}`}
          >
            {item.path ? (
              <Link
                href={item.path}
                {...(item.hasRedirectUrl ? { target: "_blank" } : {})}
                className={`block px-2 py-2 my-1 text-base hover:text-blue-600 whitespace-nowrap  ${
                  item.isButton
                    ? "bg-blue-600 text-white rounded-full px-4 font-semibold hover:text-white hover:bg-blue-600"
                    : ""
                } ${
                  item?.label?.toLowerCase() === "home"
                    ? pathName === "/" && "border-b-2 border-blue-600"
                    : pathName?.includes(item?.path) &&
                      "border-b-2 border-blue-600 text-blue-600"
                }`}
              >
                <span className="inline-flex items-center">
                  {item.label}
                  {hasChildren && <ChevronDown className="ml-1 h-4 w-4" />}
                </span>
              </Link>
            ) : (
              <button
                className={`block px-2 py-2 text-left text-base hover:text-blue-600 whitespace-nowrap`}
              >
                <span className="inline-flex items-center">
                  {item.label}
                  {hasChildren && <ChevronDown className="ml-1 h-4 w-4" />}
                </span>
              </button>
            )}

            {/* Nested submenu */}
            {hasChildren && isOpen && (
              <div
                className={`${
                  depth === 0
                    ? "absolute left-0 top-9"
                    : "absolute left-full top-0"
                }`}
              >
                <NavMenu items={item?.children} depth={depth + 1} />
              </div>
            )}
          </li>
        );
      })}
    </ul>
  );
};

export default NavMenu;
