import React from "react";
import {
  Control,
  Controller,
  FieldValues,
  Path,
  RegisterOptions,
  useFormState,
} from "react-hook-form";
import ReCA<PERSON><PERSON><PERSON> from "react-google-recaptcha";
import get from "lodash/get";

type FilteredRegisterOptions<T extends FieldValues> = Omit<
  RegisterOptions<T>,
  "valueAsNumber" | "valueAsDate" | "setValueAs" | "disabled"
>;

interface CaptchaInputProps<T extends FieldValues> {
  control: Control<T>;
  name: Path<T>;
  label?: string;
  rules?: FilteredRegisterOptions<T>;
  required?: boolean | string;
  className?: string;
}

const CaptchaInput = <T extends FieldValues>({
  control,
  name,
  label,
  rules,
  required = true,
  className = "",
}: CaptchaInputProps<T>) => {
  const { errors } = useFormState({ control });

  const error = get(errors, `${name}.message`) as string | undefined;

  const siteKey = "6LeiTIMrAAAAAIeeikGJzHNTVbZB-1W20iReEWtt";
  const captchaRules = {
    ...rules,
    required:
      rules?.required !== undefined
        ? rules.required
        : required
        ? typeof required === "string"
          ? required
          : "Captcha verification is required"
        : undefined,
  };

  return (
    <div className={`flex flex-col items-start gap-2 ${className}`}>
      {label && (
        <label className="text-sm font-semibold text-gray-700">
          {label} {required && <span className="text-red-500">*</span>}
        </label>
      )}
      <Controller
        name={name}
        control={control}
        rules={captchaRules}
        render={({ field: { onChange } }) => (
          <ReCAPTCHA sitekey={siteKey} onChange={(value) => onChange(value)} />
        )}
      />
      {error && <p className="text-sm text-red-500">{error}</p>}
    </div>
  );
};

export default CaptchaInput;
