import get from "lodash/get";
import {
  Control,
  Controller,
  FieldValues,
  Path,
  RegisterOptions,
  useFormState,
} from "react-hook-form";

type FilteredRegisterOptions<T extends FieldValues> = Omit<
  RegisterOptions<T>,
  "valueAsNumber" | "valueAsDate" | "setValueAs" | "disabled"
>;

interface CheckboxInputProps<T extends FieldValues> {
  control: Control<T>;
  name: Path<T>;
  label?: string;
  rules?: FilteredRegisterOptions<T>;
  required?: boolean;
  className?: string;
}

const CheckboxInput = <T extends FieldValues>({
  control,
  name,
  label,
  rules,
  required,
  className,
}: CheckboxInputProps<T>) => {
  const { errors } = useFormState({ control });
  const error = get(errors, `${name}.message`) as string | undefined;

  const inputRules = {
    ...rules,
    required:
      rules?.required !== undefined
        ? rules.required
        : required
        ? typeof required === "string"
          ? required
          : "Required"
        : undefined,
  };

  return (
    <div className={`relative ${className}`}>
      <Controller
        name={name}
        control={control}
        rules={inputRules}
        render={({ field }) => (
          <label className="flex items-center space-x-2 text-sm text-gray-800">
            <input
              type="checkbox"
              checked={field.value || false}
              onChange={(e) => field.onChange(e.target.checked)}
              className="accent-blue-600"
            />
            {label && (
              <span>
                {label}
                {required && <span className="text-red-500 ml-1">*</span>}
              </span>
            )}
          </label>
        )}
      />
      {error && <p className="text-sm text-red-500 mt-1">{error}</p>}
    </div>
  );
};

export default CheckboxInput;
