// /* eslint-disable @typescript-eslint/no-explicit-any */
// import { AppSpinner } from "@/app/admin/common/app-spinner";
// import {
//   FAX_VALIDATION_RULE,
//   PHONE_VALIDATION_RULE,
// } from "@/constant/regexp.constants";
// import { cn } from "@/utils/utils";
// import get from "lodash/get";
// import { useRef } from "react";
// import {
//   Control,
//   Controller,
//   FieldValues,
//   Path,
//   RegisterOptions,
//   useFormState,
// } from "react-hook-form";
// import { PatternFormat } from "react-number-format";

// interface PhoneInputWidgetProps<T extends FieldValues> {
//   name: Path<T>;
//   label?: string;
//   control: Control<T>;
//   placeholder?: string;
//   className?: string;
//   inputClassName?: string;
//   onChange?: (value: string) => void;
//   onBlur?: (value: string) => void;
//   disabled?: boolean;
//   readOnly?: boolean;
//   rules?: RegisterOptions<T, Path<T>>;
//   isFax?: boolean;
//   countryCode?: string;
//   hasExt?: boolean;
//   autoComplete?: string;
//   required?: boolean;
//   loading?: boolean;
// }

// export const normalizePhoneValue = (
//   value: string = "",
//   countryCode: string = "+1"
// ): string => {
//   const [numberPart, extPart] = String(value || "")?.split("x"); // Safe now because value defaults to ''

//   const trimmedNumber = numberPart?.replace(/[^0-9+]/g, "").slice(0, 10);
//   const trimmedExt = extPart?.replace(/[^0-9]/g, "");
//   const number = trimmedExt ? `${trimmedNumber} x${trimmedExt}` : trimmedNumber;

//   return number ? `${countryCode}${number}` : "";
// };

// const removeCountryCodePrefix = (value: string = "", countryCode: string) => {
//   return value?.startsWith(countryCode)
//     ? value?.slice(countryCode?.length)
//     : value;
// };

// const PhoneInput = <T extends FieldValues>({
//   name,
//   label,
//   control,
//   hasExt,
//   placeholder = hasExt ? "(____) ___-___ x_____" : "(____) ___-___",
//   className,
//   inputClassName,
//   onChange,
//   disabled,
//   rules,
//   isFax,
//   countryCode = "+1",
//   autoComplete = "tel",
//   required,
//   loading,
// }: PhoneInputWidgetProps<T>) => {
//   const inputRules = {
//     ...rules,
//     required:
//       rules?.required !== undefined
//         ? rules.required
//         : required
//         ? typeof required === "string"
//           ? required
//           : "Required"
//         : undefined,
//   };

//   const { errors } = useFormState({ control });
//   const error = get(errors, `${name}.message`) as string | undefined;
//   const hasUserInteracted = useRef(false);

//   const renderPhoneInput = (field: any = {}) => (
//     <div className="relative w-full">
//       <PatternFormat
//         type="tel"
//         format={hasExt ? "(###) ###-#### x#####" : "(###) ###-####"}
//         mask="_"
//         value={removeCountryCodePrefix(field?.value, countryCode) ?? ""}
//         onValueChange={({ formattedValue }) => {
//           const value = normalizePhoneValue(formattedValue, countryCode);
//           // ✅ Only call onChange if user has interacted
//           if (hasUserInteracted.current) {
//             field?.onChange?.(value); // react-hook-form
//             onChange?.(value); // your external handler
//           }
//         }}
//         onFocus={() => {
//           hasUserInteracted.current = true; // 🟢 Set flag on first user interaction
//         }}
//         disabled={disabled}
//         placeholder={placeholder}
//         autoComplete={autoComplete}
//         className={cn(
//           "w-full border rounded-md py-1.5 ps-4 pe-2 placeholder:text-[#B3B3B3]",
//           "border-gray-300 focus:border-[#2563eb] focus:ring-1 focus:ring-[#2563eb] focus:outline-none",
//           "transition-all duration-150 ease-in-out",
//           error && "border-red-500",
//           disabled && "bg-gray-300 opacity-70 cursor-not-allowed",
//           inputClassName
//         )}
//       />
//       <AppSpinner
//         loading={loading}
//         className="primary absolute right-2 top-2"
//       />
//     </div>
//   );

//   return (
//     <div className={cn("flex flex-col", className)}>
//       {label && (
//         <label
//           htmlFor={name}
//           className="block text-sm font-semibold text-gray-700 pb-1"
//         >
//           {label}{" "}
//           {inputRules?.required && <span className="text-red-500">*</span>}
//         </label>
//       )}

//       {control ? (
//         <Controller
//           name={name}
//           control={control}
//           render={({ field }) => renderPhoneInput(field)}
//           rules={{
//             ...inputRules,
//             ...(isFax ? FAX_VALIDATION_RULE : PHONE_VALIDATION_RULE),
//           }}
//         />
//       ) : (
//         renderPhoneInput()
//       )}

//       {error && <p className="text-sm text-red-500 mt-1">{error}</p>}
//     </div>
//   );
// };

// export default PhoneInput;
