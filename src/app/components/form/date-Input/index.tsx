import get from "lodash/get";
import {
  Control,
  Controller,
  FieldValues,
  Path,
  RegisterOptions,
  useFormState,
} from "react-hook-form";

type FilteredRegisterOptions<T extends FieldValues> = Omit<
  RegisterOptions<T>,
  "valueAsNumber" | "valueAsDate" | "setValueAs" | "disabled"
>;

interface DateInputProps<T extends FieldValues> {
  control: Control<T>;
  name: Path<T>;
  label?: string;
  placeholder?: string;
  rules?: FilteredRegisterOptions<T>;
  required?: boolean;
  className?: string;
  inputClassName?: string;
}

const DateInput = <T extends FieldValues>({
  control,
  name,
  label,
  placeholder = "Select date",
  rules,
  required = false,
  className,
  inputClassName,
}: DateInputProps<T>) => {
  const { errors } = useFormState({ control });
  const error = get(errors, `${name}.message`) as string | undefined;

  const inputRules = {
    ...rules,
    required:
      rules?.required !== undefined
        ? rules.required
        : required
        ? typeof required === "string"
          ? required
          : "Required"
        : undefined,
  };

  return (
    <div className={`relative ${className}`}>
      {label && (
        <label
          htmlFor={name}
          className="block text-sm font-semibold text-gray-700"
        >
          {label} {required && <span className="text-red-500">*</span>}
        </label>
      )}
      <Controller
        name={name}
        control={control}
        rules={inputRules}
        render={({ field }) => (
          <div className="relative mt-1">
            <input
              {...field}
              id={name}
              type="date"
              placeholder={placeholder}
              className={`w-full bg-white text-sm text-gray-800 pr-10 border border-gray-300 focus:ring-2 focus:ring-blue-500 px-4 py-2 rounded-md outline-none ${
                error ? "bg-red-50 border-red-400" : ""
              } ${inputClassName}`}
            />

            {error && <p className="text-sm text-red-500 mt-1">{error}</p>}
          </div>
        )}
      />
    </div>
  );
};

export default DateInput;
