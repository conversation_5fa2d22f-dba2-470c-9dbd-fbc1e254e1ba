// import { AppSpinner } from "@/app/admin/common/app-spinner";

import get from "lodash/get";
import { X } from "lucide-react";
import {
  Control,
  Controller,
  FieldValues,
  Path,
  RegisterOptions,
  useFormState,
} from "react-hook-form";

type FilteredRegisterOptions<T extends FieldValues> = Omit<
  RegisterOptions<T>,
  "valueAsNumber" | "valueAsDate" | "setValueAs" | "disabled"
>;

export interface OptionTypes {
  label: string;
  value: string | number;
}

interface SelectInputProps<T extends FieldValues> {
  control: Control<T>;
  name: Path<T>;
  label?: string;
  placeholder?: string;
  rules?: FilteredRegisterOptions<T>;
  required?: boolean;
  options: OptionTypes[];
  clearable?: boolean;
  className?: string;
  selectClassName?: string;
  disabled?: boolean;
  loading?: boolean;
}

const SelectInput = <T extends FieldValues>({
  control,
  name,
  label,
  placeholder = "Select",
  rules,
  required = false,
  options,
  clearable = false,
  className,
  selectClassName,
  disabled,
  loading,
}: SelectInputProps<T>) => {
  const { errors } = useFormState({ control });
  const error = get(errors, `${name}.message`) as string | undefined;

  const inputRules = {
    ...rules,
    required:
      rules?.required !== undefined
        ? rules.required
        : required
        ? typeof required === "string"
          ? required
          : "Required"
        : undefined,
  };

  return (
    <div className={`relative ${className}`}>
      {label && (
        <label
          htmlFor={name}
          className="block text-sm font-semibold text-gray-700"
        >
          {label} {required && <span className="text-red-500">*</span>}
        </label>
      )}
      <Controller
        name={name}
        control={control}
        rules={inputRules}
        render={({ field }) => (
          <div className="relative mt-1">
            <select
              {...field}
              id={name}
              defaultValue={field?.value}
              className={`bg-white w-full py-2 px-3 rounded-md border border-gray-300 focus-within:bg-white outline-blue-600",
                ${error ? "bg-red-50 border-red-400" : ""},
                ${
                  disabled
                    ? "bg-gray-300 opacity-70 rounded-md border border-gray-400 cursor-not-allowed"
                    : ""
                },
                ${selectClassName}
              `}
              disabled={loading || disabled}
            >
              {/* Placeholder option */}
              <option value="" disabled selected>
                {placeholder}
              </option>
              {options?.map((opt) => (
                <option key={opt?.value} value={opt?.value}>
                  {opt?.label}
                </option>
              ))}
            </select>
            {clearable && (
              <button
                type="button"
                onClick={() => field?.onChange("")}
                className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                aria-label="Clear selection"
              >
                <X size={18} />
              </button>
            )}

            {error && <p className="text-sm text-red-500 mt-1">{error}</p>}
          </div>
        )}
      />
    </div>
  );
};

export default SelectInput;
