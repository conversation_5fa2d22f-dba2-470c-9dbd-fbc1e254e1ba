import get from "lodash/get";
import React, { HTMLInputTypeAttribute } from "react";
import {
  Control,
  Controller,
  FieldValues,
  Path,
  RegisterOptions,
  useFormState,
} from "react-hook-form";

type FilteredRegisterOptions<T extends FieldValues> = Omit<
  RegisterOptions<T>,
  "valueAsNumber" | "valueAsDate" | "setValueAs" | "disabled"
>;

interface TextInputProps<T extends FieldValues> {
  control: Control<T>;
  name: Path<T>;
  label?: string;
  placeholder?: string;
  rules?: FilteredRegisterOptions<T>;
  type?: HTMLInputTypeAttribute;
  inputMode?:
    | "search"
    | "text"
    | "email"
    | "tel"
    | "url"
    | "none"
    | "numeric"
    | "decimal"
    | undefined;
  required?: boolean;
  icon?: React.ReactNode;
  endIcon?: React.ReactNode;
  className?: string;
  inputClassName?: string;
  maxLength?: number;
}

const TextInput = <T extends FieldValues>({
  control,
  name,
  label,
  placeholder = "",
  type = "text",
  inputMode,
  required = false,
  rules,
  icon,
  endIcon,
  className = "",
  inputClassName,
  maxLength,
}: TextInputProps<T>) => {
  // Get all errors from form state
  const { errors } = useFormState({ control });

  const error = get(errors, `${name}.message`) as string | undefined;

  const inputRules = {
    ...rules,
    required:
      rules?.required !== undefined
        ? rules.required
        : required
        ? typeof required === "string"
          ? required
          : "Required"
        : undefined,
  };

  return (
    <div className={`relative ${className}`}>
      {label && (
        <label
          htmlFor={name}
          className="block text-sm font-semibold text-gray-700"
        >
          {label} {required && <span className="text-red-500">*</span>}
        </label>
      )}
      <Controller
        name={name}
        control={control}
        rules={inputRules}
        render={({ field }) => (
          <div className="relative mt-1">
            {icon && (
              <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                {icon}
              </div>
            )}
            <input
              {...field}
              id={name}
              type={type}
              inputMode={inputMode}
              maxLength={maxLength}
              placeholder={placeholder}
              className={`w-full bg-white text-sm text-gray-800 pr-10 border border-gray-300 focus-within:bg-white px-4 py-2 rounded-md outline-blue-600 ${
                error ? "bg-red-50 border-red-400" : ""
              } ${icon ? "ps-10" : ""} ${inputClassName}`}
              onInput={(e) => {
                if (type === "tel") {
                  e.currentTarget.value = e.currentTarget.value.replace(
                    /[^0-9]/g,
                    ""
                  );
                }
              }}
            />
            {endIcon && (
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                {endIcon}
              </div>
            )}
            {error && <p className="text-sm text-red-500 mt-1">{error}</p>}
          </div>
        )}
      />
    </div>
  );
};

export default TextInput;
