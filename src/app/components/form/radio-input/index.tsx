import get from "lodash/get";
import {
  Control,
  Controller,
  FieldValues,
  Path,
  RegisterOptions,
  useFormState,
} from "react-hook-form";

type FilteredRegisterOptions<T extends FieldValues> = Omit<
  RegisterOptions<T>,
  "valueAsNumber" | "valueAsDate" | "setValueAs" | "disabled"
>;

type Option = {
  label: string;
  value: string;
};

interface RadioInputProps<T extends FieldValues> {
  control: Control<T>;
  name: Path<T>;
  label?: string;
  options: Option[];
  rules?: FilteredRegisterOptions<T>;
  required?: boolean;
  clearable?: boolean;
  className?: string;
  radioClassName?: string;
  onChange?: (value: string) => void;
}

const RadioInput = <T extends FieldValues>({
  control,
  name,
  label,
  options,
  rules,
  required = false,
  className,
  radioClassName,
  onChange,
}: RadioInputProps<T>) => {
  const { errors } = useFormState({ control });
  const error = get(errors, `${name}.message`) as string | undefined;

  const inputRules = {
    ...rules,
    required:
      rules?.required !== undefined
        ? rules.required
        : required
        ? typeof required === "string"
          ? required
          : "Required"
        : undefined,
  };

  return (
    <div className={`relative ${className}`}>
      {label && (
        <label
          htmlFor={name}
          className="block text-sm font-semibold text-gray-700 mb-2"
        >
          {label} {required && <span className="text-red-500">*</span>}
        </label>
      )}
      <Controller
        name={name}
        control={control}
        rules={inputRules}
        render={({ field }) => (
          <div className={`flex items-center gap-3 ${radioClassName}`}>
            {options?.map((opt, index: number) => (
              <label
                key={`${opt?.value}-${index}`}
                className="flex items-center space-x-2 text-sm text-gray-800"
              >
                <input
                  type="radio"
                  value={opt?.value}
                  checked={field?.value === opt?.value}
                  onChange={() => {
                    field.onChange(opt?.value);
                    onChange?.(opt?.value);
                  }}
                  className="accent-blue-600"
                />
                <span>{opt?.label}</span>
              </label>
            ))}

            {error && <p className="text-sm text-red-500 mt-1">{error}</p>}
          </div>
        )}
      />
    </div>
  );
};

export default RadioInput;
