/* eslint-disable @typescript-eslint/no-explicit-any */
import get from "lodash/get";
import { useEffect, useRef } from "react";
import {
  Control,
  Controller,
  FieldValues,
  Path,
  useFormState,
} from "react-hook-form";
import SignaturePad from "signature_pad";

interface SignatureInputProps<T extends FieldValues> {
  control: Control<T>;
  name: Path<T>;
  label?: string;
  required?: boolean;
  className?: string;
  canvasClassName?: string;
}

const SignatureInput = <T extends FieldValues>({
  control,
  name,
  label,
  required = false,
  className = "",
  canvasClassName = "",
}: SignatureInputProps<T>) => {
  const { errors } = useFormState({ control });
  const error = get(errors, `${name}.message`) as string | undefined;

  const canvasRef = useRef<HTMLCanvasElement | null>(null);
  const signaturePadRef = useRef<SignaturePad | null>(null);

  // Resize canvas properly (important for responsiveness)
  const resizeCanvas = () => {
    if (canvasRef.current) {
      const canvas = canvasRef.current;
      const ratio = Math.max(window.devicePixelRatio || 1, 1);
      canvas.width = canvas.offsetWidth * ratio;
      canvas.height = canvas.offsetHeight * ratio;
      canvas.getContext("2d")?.scale(ratio, ratio);
      signaturePadRef.current?.clear();
    }
  };
  let controllerField: any;

  useEffect(() => {
    if (canvasRef.current) {
      signaturePadRef.current = new SignaturePad(canvasRef.current, {
        backgroundColor: "white",
        penColor: "black",
      });

      // Sync with RHF on every stroke
      signaturePadRef.current.addEventListener("endStroke", () => {
        if (signaturePadRef.current && !signaturePadRef.current.isEmpty()) {
          const dataUrl = signaturePadRef.current.toDataURL("image/png");
          controllerField?.onChange(dataUrl);
        }
      });

      resizeCanvas();
      window.addEventListener("resize", resizeCanvas);

      return () => {
        window.removeEventListener("resize", resizeCanvas);
      };
    }
  }, [controllerField]);

  // Store controller field reference to update inside signature events

  return (
    <div className={`relative ${className}`}>
      {label && (
        <label className="block text-sm font-semibold text-gray-700">
          {label} {required && <span className="text-red-500">*</span>}
        </label>
      )}

      <Controller
        name={name}
        control={control}
        rules={{ required: required ? "Required" : false }}
        render={({ field }) => {
          controllerField = field;

          return (
            <div className="relative mt-2">
              <div className="relative inline-block w-full">
                {/* Signature area */}
                <canvas
                  ref={canvasRef}
                  className={`border border-dashed rounded-md border-gray-400 w-full h-36 bg-white cursor-crosshair ${
                    error ? "border-red-500" : ""
                  } ${canvasClassName}`}
                />

                {/* Clear button */}
                <button
                  type="button"
                  onClick={() => {
                    signaturePadRef.current?.clear();
                    field.onChange("");
                    field.onBlur();
                  }}
                  className="absolute top-2 right-2 px-2 py-1 bg-gray-200 rounded hover:bg-gray-300 text-xs shadow"
                >
                  Clear
                </button>

                {/* Overlay hint */}
                {(!field.value || signaturePadRef.current?.isEmpty()) && (
                  <div className="opacity-40 absolute inset-0 flex items-center justify-center pointer-events-none text-gray-400 text-sm">
                    ✍️ Sign here
                  </div>
                )}
              </div>

              {error && <p className="text-sm text-red-500 mt-1">{error}</p>}
            </div>
          );
        }}
      />
    </div>
  );
};

export default SignatureInput;
