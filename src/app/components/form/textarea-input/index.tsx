import get from "lodash/get";
import React from "react";
import {
  Control,
  Controller,
  FieldValues,
  Path,
  useFormState,
} from "react-hook-form";

interface TextAreaInputProps<T extends FieldValues> {
  control: Control<T>;
  name: Path<T>;
  label?: string;
  placeholder?: string;
  required?: boolean;
  className?: string;
  inputClassName?: string;
  rows?: number;
  cols?: number;
}

const TextAreaInput = <T extends FieldValues>({
  control,
  name,
  label,
  placeholder = "",
  required = false,
  className = "",
  inputClassName,
  rows = 5,
  cols = 5,
}: TextAreaInputProps<T>) => {
  // Get all errors from form state
  const { errors } = useFormState({ control });

  const error = get(errors, `${name}.message`) as string | undefined;

  return (
    <div className={`relative ${className}`}>
      {label && (
        <label
          htmlFor={name}
          className="block text-sm font-semibold text-gray-700"
        >
          {label} {required && <span className="text-red-500">*</span>}
        </label>
      )}
      <Controller
        name={name}
        control={control}
        rules={{ required: required ? "Required" : false }}
        render={({ field }) => (
          <div className="relative mt-1">
            <textarea
              {...field}
              id={name}
              rows={rows}
              cols={cols}
              placeholder={placeholder}
              className={`w-full bg-white text-sm text-gray-800 pr-10 border border-gray-300 focus-within:bg-white px-4 py-2 rounded-md outline-blue-600 ${
                error ? "bg-red-50 border-red-400" : ""
              } ${inputClassName}`}
            />
            {error && <p className="text-sm text-red-500 mt-1">{error}</p>}
          </div>
        )}
      />
    </div>
  );
};

export default TextAreaInput;
