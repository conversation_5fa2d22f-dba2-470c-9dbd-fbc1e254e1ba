"use client";
import { <PERSON><PERSON><PERSON>, Ch<PERSON>ronUp, Clock, MapPin, Phone } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import veternaryLogo from "../../../../public/logos/Dry-creek-veterinary-hospital-logo.webp";

const Footer = () => {
  const router = useRouter();
  const pathName = usePathname();

  const currentYear = new Date().getFullYear();

  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 200) {
        setIsVisible(true);
      } else {
        setIsVisible(false);
      }
    };

    window.addEventListener("scroll", handleScroll);

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  return (
    <>
      <footer className="bg-blue-600 text-white  w-full z-10 pt-5">
        <div className="container mx-auto px-4 lg:px-8 py-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 md:pt-12">
            {/* Logo + Socials */}
            {/* Logo + Socials */}
            <div className="flex flex-col items-start w-full">
              <Image
                src={veternaryLogo}
                alt="Veterinary Medical Center "
                width={120}
                height={200}
                loading="eager"
                className="h-auto cursor-pointer object-contain p-2 bg-white rounded"
                onClick={() => router?.push("/")}
              />
              <ul className="flex mt-4 space-x-3">
                <li className="bg-blue-100 rounded p-1 cursor-pointer">
                  <a
                    href="https://www.instagram.com/drycreekvethospital/"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <Image
                      src={"/images/instagram.avif"}
                      alt="instagram icon"
                      width={25}
                      height={25}
                      loading="lazy"
                      quality={80}
                    />
                  </a>
                </li>
                <li className="bg-blue-100 rounded p-1 cursor-pointer">
                  <a
                    href="https://www.facebook.com/DryCreekVetHospital/"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <Image
                      src={"/images/facebook.avif"}
                      alt="facebook icon"
                      width={25}
                      height={25}
                      loading="lazy"
                      quality={80}
                    />
                  </a>
                </li>
                <li className="bg-blue-100 rounded p-1 cursor-pointer">
                  <a
                    href="https://www.yelp.com/biz/dry-creek-veterinary-hospital-galt"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <Image
                      src={"/images/yelp.png"}
                      alt="Yelp icon"
                      width={25}
                      height={25}
                      loading="lazy"
                      quality={80}
                    />
                  </a>
                </li>
              </ul>
            </div>

            {/* Contact Info */}
            <div>
              <ul className="space-y-6">
                <li className="flex items-start">
                  <MapPin className="text-secondary flex-shrink-0" size={24} />
                  <div className="ml-4">
                    <p className="text-white text-md font-bold">Location</p>
                    <p className="text-sm text-white mt-1">
                      <a
                        href="https://maps.app.goo.gl/ctmaJW5UUy8q2TpLA"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="hover:text-sky-300"
                      >
                        1000 C Street #110 Galt, CA 95632
                      </a>
                    </p>
                  </div>
                </li>
                <li className="flex items-start">
                  <Phone className="text-secondary flex-shrink-0" size={24} />
                  <div className="ml-4">
                    <p className="text-white text-md font-bold">Contact</p>
                    <p className="text-sm text-white mt-1">
                      <strong>Call Us</strong>:{" "}
                      <a
                        itemProp="telephone"
                        content="2097459130"
                        href="tel:+12097459130"
                        className="hover:text-sky-300"
                      >
                        (*************
                      </a>
                    </p>
                    <p className="text-sm text-white">
                      <strong>E-mail</strong>:{" "}
                      <a
                        href="mailto:<EMAIL>"
                        className="hover:text-gray-300"
                      >
                        <EMAIL>
                      </a>
                    </p>
                  </div>
                </li>
                <li className="flex items-start">
                  <Clock className="text-secondary flex-shrink-0" size={24} />
                  <div className="ml-4">
                    <p className="text-white text-md font-bold">Hours</p>
                    <p className="text-sm text-white mt-1">
                      <strong>Mon – Fri</strong>: 8:00 AM – 7:00 PM <br />
                      <strong>Sat</strong>: 9:00 AM - 5:00 PM
                    </p>
                    <p className="text-sm text-white">
                      <strong>Sunday</strong>: Closed
                    </p>
                  </div>
                </li>
              </ul>
            </div>

            {/* Quick Links */}
            <div>
              <p className="text-base font-semibold mb-2 text-white">
                Quick Links
              </p>
              <ul className="list-disc list-inside lg:text-sm text-xs">
                <li className="p-1">
                  <Link
                    href="/about-dry-creek-veterinary-hospital/"
                    className="text-white hover:underline"
                  >
                    About Us
                  </Link>
                </li>
                <li className="p-1">
                  <Link
                    href="/services/"
                    className="text-white hover:underline"
                  >
                    Services
                  </Link>
                </li>
                <li className="p-1">
                  <Link href="/blogs/" className="text-white hover:underline">
                    Blogs
                  </Link>
                </li>
                <li className="p-1">
                  <Link
                    href="/payment-option/"
                    className="text-white hover:underline"
                  >
                    Payment Options
                  </Link>
                </li>
                <li className="p-1">
                  <Link
                    href="/helpful-links/"
                    className="text-white hover:underline"
                  >
                    Helpful Links
                  </Link>
                </li>
                <li className="p-1">
                  <Link
                    href="/contact-us"
                    className="text-white hover:underline"
                  >
                    Contact
                  </Link>
                </li>
              </ul>
            </div>

            {/* Google Map */}
            <div className="rounded-xl overflow-hidden">
              <iframe
                width="100%"
                height="250"
                title="Map of Veterinary Medical Center "
                loading="lazy"
                className="rounded-lg border-0 w-full h-56 md:h-64"
                src="https://www.google.com/maps/embed?pb=!1m14!1m8!1m3!1d391.6450757128175!2d-121.2985612!3d38.252581!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x809aa2daa1000115%3A0xa4c5202aa23f2502!2sDry%20Creek%20Veterinary%20Hospital!5e0!3m2!1sen!2sin!4v1756117218307!5m2!1sen!2sin"
                allowFullScreen
              ></iframe>
            </div>
          </div>

          <div className="mt-8 md:mt-15 flex-auto text-center items-center">
            <p className="">
              <span className="text-sm md:text-md">
                {" "}
                © {currentYear} Dry Creek Veterinary Hospital. All Rights
                Reserved | Made with ❤️ for pets and their families
                {/* <strong>
                  Part of the PetVet Care Centers Network. Managed with Tymbrel
                </strong>{" "} */}{" "}
                |{" "}
                <Link
                  href={"../privacy-policy"}
                  className="cursor-pointer hover:underline"
                >
                  Privacy Policy{" "}
                </Link>
                | Powered by{" "}
              </span>
              <Link
                href={"https://myvethub.com/"}
                target="_blank"
                className="cursor-pointer hover:underline"
              >
                <span>
                  <span className="text-secondary font-dancing font-bold text-md  tracking-wide">
                    myvethub.com
                  </span>
                </span>
              </Link>
            </p>
          </div>

          {!pathName.includes("/upload-details/") &&
            !pathName.includes("/payment-success/") &&
            !pathName.includes("/payment-failed/") &&
            !pathName?.includes("/booking-success/") &&
            !pathName?.includes("/booking-appointment/") && (
              <button
                title="Book Appointment"
                onClick={() => router.push("/booking-appointment")}
                className="md:hidden fixed bottom-4 right-4 z-50 inline-flex items-center justify-center w-14 h-14 rounded-full bg-blue-600"
              >
                <div className="absolute z-10 top-0 left-0 w-full h-full rounded-full bg-blue-600 animate-ping"></div>
                <div className="relative z-20">
                  <CalendarClock color="white" />
                </div>
              </button>
            )}

          {!pathName.includes("/upload-details/") &&
            !pathName.includes("/booking-failed/") &&
            !pathName?.includes("/booking-success/") &&
            !pathName?.includes("/booking-appointment/") && (
              <button
                onClick={() => router.push("/booking-appointment")}
                className="hidden fixed bottom-4  md:right-1 lg:right-4 z-50 md:inline-flex items-center justify-center  tracking-wide cursor-pointer leading-4 py-2 px-4 md:px-1 lg:px-6 rounded-full border-2 text-white border-[#fff] group bg-blue-600 transition duration-300 ease-in-out"
              >
                <p className="font-medium text-lg">
                  Book Appointment
                </p>
              </button>
            )}

          {isVisible && (
            <a
              href="#"
              onClick={(e) => {
                e.preventDefault();
                window.scrollTo({
                  top: 0,
                  behavior: "smooth", // Smooth scrolling
                });
              }}
            >
              <button className="fixed shadow-md bottom-28 right-5 flex items-center justify-center w-11 h-11 bg-blue-600 hover:bg-blue-600 rounded-full border-none cursor-pointer group">
                <div
                  title="Go To Top"
                  className="transform translate-y-0 transition-transform duration-700 group-hover:-translate-y-1"
                >
                  <ChevronUp
                    size={22}
                    className="font-extrabold  hover:!text-white"
                  />
                </div>
              </button>
            </a>
          )}
        </div>
      </footer>
    </>
  );
};
export default Footer;
