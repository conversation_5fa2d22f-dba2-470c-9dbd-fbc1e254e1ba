"use client";

import { useEffect, useState } from "react";
import Image from "next/image";
import { ChevronLeft, ChevronRight } from "lucide-react";

type Slide = {
  id: number | string;
  src: string;
  alt: string;
};

type SliderProps = {
  slides: Slide[];
  height?: string; // e.g. "h-[400px]"
  width?: string;
  autoPlay?: boolean;
  interval?: number; // ms
};

export default function Slider({
  slides,
  height = "h-[400px]",
  width = "w-[700px]",
  autoPlay = false,
  interval = 10000,
}: SliderProps) {
  const [current, setCurrent] = useState(0);

  // Random starting slide (no repeat same image on refresh)
  useEffect(() => {
    const randomIndex = Math.floor(Math.random() * slides.length);
    setCurrent(randomIndex);
  }, [slides.length]);

  // Autoplay
  useEffect(() => {
    if (!autoPlay) return;
    const timer = setInterval(() => {
      setCurrent((prev) => (prev + 1) % slides.length);
    }, interval);
    return () => clearInterval(timer);
  }, [autoPlay, interval, slides.length]);

  const nextSlide = () => setCurrent((prev) => (prev + 1) % slides.length);
  const prevSlide = () =>
    setCurrent((prev) => (prev - 1 + slides.length) % slides.length);

  return (
    <div
      className={`relative w-full max-w-6xl mx-auto overflow-hidden rounded-3xl shadow-2xl z-1`}
    >
      {/* Slider container */}
      <div
        className="flex transition-transform duration-700 ease-in-out"
        style={{ transform: `translateX(-${current * 100}%)` }}
      >
        {slides.map((slide, index) => (
          <div key={slide.id} className="w-full flex-shrink-0 relative">
            <Image
              src={slide.src}
              alt={slide.alt}
              width={1200}
              height={700}
              className={`w-full object-cover object-center ${height} ${width}`}
              priority={index === current}
            />
          </div>
        ))}
      </div>

      {/* Navigation */}
      <button
        onClick={prevSlide}
        className="absolute top-1/2 left-4 -translate-y-1/2 bg-white/30 hover:bg-white/50 backdrop-blur-md p-3 rounded-full transition"
        aria-label="Previous"
      >
        <ChevronLeft className="w-6 h-6 text-gray-900" />
      </button>
      <button
        onClick={nextSlide}
        className="absolute top-1/2 right-4 -translate-y-1/2 bg-white/30 hover:bg-white/50 backdrop-blur-md p-3 rounded-full transition"
        aria-label="Next"
      >
        <ChevronRight className="w-6 h-6 text-gray-900" />
      </button>

      {/* Indicators */}
      <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex space-x-2">
        {slides.map((_, index) => (
          <button
            key={index}
            onClick={() => setCurrent(index)}
            className={`w-3 h-3 rounded-full transition ${
              current === index ? "bg-blue-600" : "bg-white/50"
            }`}
          />
        ))}
      </div>
    </div>
  );
}
