"use client";
import { Calendar } from "lucide-react";
import Link from "next/link";
import Slider from "./slider";

const WelcomeSection = () => {
  const IMAGES = [
    {
      id: 1,
      src: "/images/friendly-veterinarian-with-pet-animal-vector.webp",
      alt: "Veterinarian with pets",
    },
    {
      id: 2,
      src: "/images/vet-clinic-mascot-banner.webp",
      alt: "Dog checkup at the vet",
    },
    {
      id: 3,
      src: "/images/vet-check-upmascots.webp",
      alt: "Cat wellness care",
    },
    {
      id: 4,
      src: "/images/pet-surgery.webp",
      alt: "Pet surgery recovery",
    },
  ];

  return (
    <section
      id="home"
      className="bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center"
    >
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          <div className="space-y-8">
            <div className="space-y-4">
              <h1 className=" text-2xl lg:text-4xl font-bold text-gray-900 leading-tight">
                Welcome to
                <span className="text-blue-600 block">
                  Dry Creek Veterinary
                </span>
                <span className="text-2xl lg:text-3xl font-medium text-gray-600 block mt-2">
                  Hospital
                </span>
              </h1>
              <p className=" text-gray-600 max-w-2xl">
                Dry Creek Veterinary Hospital has been serving the Galt,
                California community since 1995, dedicated to providing
                exceptional client service and high-quality veterinary care in a
                welcoming environment. Our facility is thoughtfully designed and
                fully equipped to ensure the best possible medical treatment for
                your beloved dogs and cats.
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 w-fit">
              <Link href="/contact-us">
                <button className="bg-blue-600 w-full text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-blue-700 transition-colors flex items-center justify-center cursor-pointer">
                  <Calendar className="w-5 h-5 mr-2" />
                  Book Appointment
                </button>
              </Link>
              {/* <Link
                href="tel:**********"
                className="border-2 border-blue-600 text-blue-600 px-8 py-4 rounded-full text-lg font-semibold hover:bg-blue-600 hover:text-white transition-colors flex items-center justify-center"
              >
                <Phone className="w-5 h-5 mr-2" />
                Emergency Call
              </Link> */}
            </div>

            {/* <div className="flex items-center space-x-6 pt-4">
              <div className="flex items-center">
                <MapPin className="w-5 h-5 text-gray-500 mr-2" />
                <span className="text-gray-600">Galt, California</span>
              </div>
              <div className="flex items-center">
                <Clock className="w-5 h-5 text-gray-500 mr-2" />
                <span className="text-gray-600">Open 24/7</span>
              </div>
            </div> */}
          </div>
          <Slider
            slides={IMAGES}
            height="h-[400px] md:h-[550px]"
            width="w-[500px]"
            autoPlay
            // interval={4000}
          />
          <div className="relative flex justify-end">
            <div className="absolute z-0 -top-10 md:-right-6 w-24 h-24 bg-blue-200 rounded-full opacity-50"></div>
            <div className="absolute z-0 -bottom-15 -left-8 w-32 h-32 bg-green-200 rounded-full opacity-30"></div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default WelcomeSection;
