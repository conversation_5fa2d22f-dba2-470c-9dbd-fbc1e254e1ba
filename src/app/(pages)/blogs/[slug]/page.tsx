import { Link } from "lucide-react";
import { BLOG_LIST } from "../blog.constants";

export default async function Page({
  params,
}: {
  params: Promise<{ slug: string }>;
}) {
  const { slug } = await params;
  const post = BLOG_LIST?.find((post) => post?.id === slug);

  if (!post) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen px-6 py-12 bg-gray-50">
        <div className="max-w-2xl text-center">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Post Not Found
          </h1>
          <p className="text-lg text-gray-600 mb-8">
            The blog post you’re looking for doesn’t exist or may have been
            removed.
          </p>
          <Link
            href="/blogs"
            className="inline-block rounded-lg bg-blue-600 px-6 py-3 text-white font-semibold hover:bg-blue-700 transition-colors"
          >
            Go Back Home
          </Link>
        </div>
      </div>
    );
  }

  return post?.component ?? null;
}

export async function generateStaticParams() {
  return BLOG_LIST?.map((post) => ({
    slug: post.id,
  }));
}
