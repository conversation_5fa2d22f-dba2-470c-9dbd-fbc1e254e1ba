"use client";
import HeroBanner from "@/app/components/hero-banner";
import Image from "next/image";
import Link from "next/link";
import { BLOG_LIST } from "./blog.constants";

const BlogPage = () => {
  return (
    <div>
      <HeroBanner
        heading={
          <>
            Veterinary <span className="text-blue-600">Blogs</span>
          </>
        }
        description="Stay informed with expert insights, pet care tips, and the latest veterinary updates to keep your pets healthy and happy."
        img="/images/veterinary-services.webp"
      />
      <section className="bg-white text-black py-5 md:py-14 px-4">
        <div className="container mx-auto">
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2 xl:grid-cols-4">
            {BLOG_LIST?.map(({ id, title, image, details }) => (
              <div
                key={id}
                className="flex flex-col h-full shadow-xl pb-5 group overflow-hidden rounded-lg"
              >
                <div className="relative overflow-hidden rounded-lg">
                  <Image
                    className="object-cover object-center w-full h-64 rounded-lg lg:h-80 transition-transform duration-500 ease-in-out group-hover:scale-105"
                    src={image}
                    alt={`${title ?? ""} veterinary blogs`}
                    width={500}
                    height={320}
                  />
                </div>

                {/* Make content area grow to equal height */}
                <div className="flex flex-col flex-grow px-4">
                  <h1 className=" font-medium text-lg line-clamp-2">{title}</h1>
                  {/* <hr className="w-32 my-6 text-black" /> */}
                  <p className="flex-grow line-clamp-3 my-6">{details}</p>{" "}
                  <Link href={`/blogs/${id}`}>
                    <div className="group mt-5 inline-flex cursor-pointer items-center gap-x-1 font-medium text-primary">
                      Read More
                      <svg
                        className="shrink-0 size-4 transition ease-in-out group-hover:translate-x-1 group-focus:translate-x-1"
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <path d="m9 18 6-6-6-6" />
                      </svg>
                    </div>
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
};

export default BlogPage;
