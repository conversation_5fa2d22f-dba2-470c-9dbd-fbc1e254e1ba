import HeroBanner from "@/app/components/hero-banner";
import { CheckCircle } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { notFound } from "next/navigation";
import { PET_SERVICES, servicesList } from "../services.constants";

interface ServicePageProps {
  params: Promise<{
    petId: string;
  }>;
}
export async function generateStaticParams() {
  return servicesList?.map((item) => ({ petId: item?.path }));
}

export default async function PageDetails({ params }: ServicePageProps) {
  const { petId } = await params;

  const matchService = PET_SERVICES?.find((item) => item?.id === petId);
  if (!matchService) {
    notFound();
  }

  return (
    <div>
      <HeroBanner
        heading={
          <>
            Pet <span className="text-blue-600">{matchService?.name}</span>
          </>
        }
        description={matchService?.description}
        img={matchService?.image?.path ?? ""}
        buttonLabel="Book Appointment"
        redirectPath="/contact-us"
      />
      <section
        id="team"
        className="py-20 bg-gradient-to-br from-blue-50 to-indigo-100"
      >
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <div className="space-y-4 pb-7">
                <h2 className="md:text-2xl font-bold text-gray-900">
                  {matchService?.deatils?.heading}
                </h2>
                <p className=" text-gray-600 leading-relaxed">
                  {matchService?.deatils?.description}
                </p>
                <p className=" text-gray-600 leading-relaxed">
                  {matchService?.deatils?.featureHeading}
                </p>
                <ul className="space-y-3 text-gray-700 pl-4 list-disc list-inside marker:text-blue-600 marker:text-xl">
                  {matchService?.deatils?.features?.map((item, index) => (
                    <li key={`${item?.label}-${index}`}>
                      <span className="font-semibold">{item?.label}</span>
                      <span className="ps-1"> {item?.description}</span>
                    </li>
                  ))}
                </ul>
                {matchService?.deatils?.note && (
                  <p className=" text-gray-600 leading-relaxed">
                    {matchService?.deatils?.note}
                  </p>
                )}
              </div>
            </div>

            <div className="relative flex justify-end">
              <Image
                src={matchService?.deatils?.image?.path ?? ""}
                alt={matchService?.deatils?.image?.alt ?? ""}
                width={600}
                height={400}
                className="rounded-2xl shadow-xl"
              />
            </div>
            {matchService?.features?.map((item, index) => (
              <div
                className="space-y-4 pb-7 col-span-2"
                key={`${item?.heading}-${index}`}
              >
                <h2 className="md:text-2xl font-bold text-gray-900">
                  {item?.heading}
                </h2>
                <p className=" text-gray-600 leading-relaxed">
                  {item?.description}
                </p>
                {item?.subHeading && (
                  <p className=" text-gray-600 leading-relaxed">
                    {item?.subHeading}
                  </p>
                )}

                <ul className="space-y-3 text-gray-700 pl-4 list-disc list-inside marker:text-blue-600 marker:text-xl">
                  {item?.items?.map((fi, index) => (
                    <li key={`${fi?.label}-${index}`}>
                      <span className="font-semibold">{fi?.label}</span>
                      <span className="ps-1">{fi?.description}</span>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
            {matchService?.note && (
              <p className="text-gray-600 leading-relaxed col-span-2 mt-[-60px]">
                {matchService?.note}
              </p>
            )}
          </div>
        </div>
      </section>
      <section
        id="services"
        className="py-20 bg-gradient-to-br from-gray-50 to-blue-50"
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-2xl md:text-4xl font-bold text-gray-900 mb-4">
              Veterinary Services in Galt, CA
            </h2>
            <div className="w-24 h-1 bg-blue-600 mx-auto mb-6"></div>
          </div>

          <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {servicesList?.map((service, index) => (
              <div key={index}>
                <Link href={`/services/${service?.path}`}>
                  <div className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow group">
                    <div className="flex items-center space-x-4">
                      <div className="bg-blue-100 group-hover:bg-blue-600 transition-colors rounded-full p-3">
                        <CheckCircle className="h-6 w-6 text-blue-600 group-hover:text-white" />
                      </div>
                      <h3 className="text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                        {service?.title}
                      </h3>
                    </div>
                  </div>
                </Link>
              </div>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
}
