"use client";

import HeroBanner from "@/app/components/hero-banner";
import Image from "next/image";
import Link from "next/link";
import React from "react";
import { servicesList } from "./services.constants";

export default function PhotoGallery() {
  return (
    <div>
      <HeroBanner
        heading={
          <>
            Veterinary <span className="text-blue-600">Services</span>
          </>
        }
        description=" At Dry Creek Veterinary Hospital, you can trust that your pets
                  are in great hands. Our compassionate, skillful veterinary
                  staff are here to help when you need us most. We look forward
                  to seeing you soon."
        img="/images/veterinary-services.webp"
        redirectPath="/contact-us"
        buttonLabel="Book Appointment"
      />

      <section id="team" className="py-8 bg-blue-600">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-1 gap-12 items-center text-center">
            <div className="space-y-6">
              <h2 className="text-3xl font-bold text-white">
                Veterinary Services in Galt, CA
              </h2>
            </div>
          </div>
        </div>
      </section>

      <section id="services" className="py-20 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          {/* Gallery Grid */}
          <div className="mx-auto">
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols- gap-6">
              {servicesList?.map((image) => (
                <React.Fragment key={image.id}>
                  <div className="bg-gradient-to-r from-gray-100 via-white to-gray-200 cursor-pointer overflow-hidden rounded-lg shadow-md hover:shadow-xl transition-all duration-300 transform ">
                    <div className="aspect-w-4 aspect-h-3 relative">
                      <Image
                        src={image.src}
                        alt={image.alt}
                        width={400}
                        height={300}
                        className="w-full md:h-[280px] object-cover transition-transform duration-300"
                      />
                    </div>
                    <div className="p-3">
                      <div className="text-center text-xl font-semibold text-gray-900 mb-3">
                        {image.title}
                      </div>
                      <div className="text-center">
                        <Link
                          href={`/services/${image?.path}`}
                          className=" mb-3 text-blue-600 hover:underline"
                        >
                          Learn More →
                        </Link>
                      </div>
                    </div>
                  </div>
                </React.Fragment>
              ))}
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
