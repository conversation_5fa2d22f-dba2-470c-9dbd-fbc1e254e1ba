import HeroBanner from "@/app/components/hero-banner";
import { CheckCircle, CreditCard, DollarSign, Zap } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

// 🟦 Payment Cards JSON Data
const PAYMENT_OPTIONS = [
  {
    id: "credit-cards",
    title: "Credit Cards",
    description:
      "All major credit cards accepted including Visa, MasterCard, and American Express",
    icon: CreditCard,
    badge: "Secure & Fast",
  },
  {
    id: "cash",
    title: "Cash",
    description:
      "Traditional cash payments welcome for your convenience and peace of mind",
    icon: DollarSign,
    badge: "Instant & Simple",
  },
  {
    id: "carecredit",
    title: "CareCredit",
    description:
      "Flexible financing options with special promotional offers available",
    icon: Zap,
    badge: "Flexible Terms",
  },
];

export default function PaymentOption() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Banner */}
      <HeroBanner
        heading={
          <>
            Payment <span className="text-blue-600">Options</span>
          </>
        }
        description="Learn more about the payment options we accept below."
        img="/images/payment-option.avif"
      />

      {/* Gradient Section */}
      <section className="relative bg-gradient-to-br from-blue-50 to-indigo-100 py-24 overflow-hidden">
        {/* Background Pattern */}
        {/* <div className="absolute inset-0 opacity-10">
          <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_1px_1px,rgba(255,255,255,0.15)_1px,transparent_0)] bg-[length:50px_50px]" />
        </div> */}

        {/* Floating Orbs */}
        {/* <div className="absolute top-10 left-10 w-20 h-20 bg-blue-500/20 rounded-full blur-xl animate-pulse" />
        <div className="absolute bottom-20 right-16 w-32 h-32 bg-blue-400/20 rounded-full blur-2xl animate-pulse delay-1000" />
        <div className="absolute top-1/2 left-1/4 w-16 h-16 bg-blue-600/20 rounded-full blur-lg animate-pulse delay-500" /> */}

        <div className="container mx-auto px-4 relative z-0">
          {/* Header */}
          <div className="text-center mb-16">
            <p className="text-2xl text-gray-900 font-semibold max-w-2xl mx-auto leading-relaxed">
              We accept multiple payment options to make your experience
              seamless and convenient
            </p>
          </div>

          {/* Payment Options Grid */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 max-w-5xl mx-auto">
            {PAYMENT_OPTIONS.map(
              ({ id, title, description, icon: Icon, badge }) => (
                <div
                  key={id}
                  className="group transform hover:scale-105 transition-all duration-300 ease-out"
                >
                  <div className="relative rounded-3xl p-8  border-2 border-gray-200 rounded-2xl shadow-lg">
                    {/* Glow Effect */}
                    {/* <div className="absolute inset-0  rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-xl" /> */}

                    {/* Content */}
                    <div className="relative z-10 text-center">
                      <div className="inline-flex p-6 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl mb-6 group-hover:from-blue-400 group-hover:to-blue-500 transition-all duration-300 shadow-lg group-hover:shadow-blue-500/25">
                        <Icon className="w-10 h-10 text-white transform group-hover:scale-110 transition-transform duration-300" />
                      </div>

                      <h3 className="text-2xl font-bold text-black mb-3 transition-colors duration-300">
                        {title}
                      </h3>

                      <p className="text-gray-800 mb-4 leading-relaxed">
                        {description}
                      </p>

                      <div className="flex items-center justify-center text-black transition-colors duration-300">
                        <CheckCircle className="w-4 h-4 mr-2" />
                        <span className="text-sm font-medium">{badge}</span>
                      </div>
                    </div>
                  </div>
                </div>
              )
            )}
          </div>
        </div>
      </section>

      {/* CareCredit Details Section */}
      <div className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-8 place-items-center">
            {/* Left side - Logo */}
            <div className="flex-1">
              <Image
                src="/images/carecredit-logo.jpeg"
                alt="CareCredit Logo"
                width={500}
                height={200}
              />
            </div>

            {/* Right side - Content */}
            <div className="flex-1 ml-12">
              <div className="space-y-6">
                <h2 className="text-2xl sm:text-4xl font-bold text-blue-600 mb-4">
                  CareCredit
                </h2>

                <p className="text-gray-700 mb-6 leading-relaxed">
                  Whether it&apos;s a routine checkup or emergency surgery, you
                  shouldn&apos;t have to worry about how to get the best medical
                  care for your pet. That&apos;s why we&apos;re pleased to
                  accept the CareCredit healthcare credit card, North
                  America&apos;s leading healthcare financing program.
                  CareCredit lets you say &quot;yes&quot; to the best treatment
                  for your pet immediately, and pay for it over time with
                  monthly payments that fit easily into your budget.
                </p>

                <h3 className="text-lg font-semibold text-gray-800 mb-4">
                  Highlights of the CareCredit Program
                </h3>

                <ul className="space-y-2 mb-8 pl-5">
                  {[
                    "Extended Payment Plans",
                    "Interest-Free For 6 Months",
                    "Quick & Easy Approval",
                    "Immediate Access to Funds",
                    "No Annual Fee",
                  ].map((item, index) => (
                    <li key={index} className="flex items-start">
                      <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0" />
                      <span className="text-gray-700">{item}</span>
                    </li>
                  ))}
                </ul>
                <Link href="https://www.carecredit.com/" target="_blank">
                  <button className="bg-blue-600 hover:bg-blue-700 cursor-pointer text-white font-bold py-3 px-8 rounded-full transition-colors duration-200">
                    CARECREDIT
                  </button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
