"use client";
interface FileWithPreview extends File {
  preview?: string;
}

interface UploadProgressType {
  progress: number;
  status: "uploading" | "completed" | "error";
}

type FileProgress = {
  [key: string]: UploadProgressType;
};

import LinkButton from "@/app/components/link-button";
import OTPVerification from "@/app/components/otp-verification";
import { API_CONSTANTS } from "@/app/constant/api-constants/apiConfig";
import { useNotificationContext } from "@/app/context/NotificationContext";
import { usePostApi } from "@/app/services/useApi";
import { useFileValidation } from "@/app/utils/validateFile";
// ModernUpload.tsx
import { ArrowRight, Download, Upload, X } from "lucide-react";
import { useSearchParams } from "next/navigation";
import React, {
  ChangeEvent,
  DragEvent,
  useCallback,
  useEffect,
  useState,
} from "react";

interface AppointmentPayload {
  token: string;
  appointmentData: AppointmentData;
}

interface AppointmentData {
  client: Client;
  meetUrl: string;
  date: string;
  timezone: string;
  startTime: string; // ISO or MM/DD/YYYY format depending on backend
  endTime: string;
  id: string;
  reason?: string;
  pet?: {
    petName: string;
    petType: string;
    petAge: string;
    breed: string;
    gender: string;
    weight: string;
    weightUnit: string;
  };
}

interface Client {
  id: string;
  name: string;
}

const UploadDetails: React.FC = () => {
  // const router = useRouter();
  const [isDragging, setIsDragging] = useState<boolean>(false);
  const [files, setFiles] = useState<FileWithPreview[]>([]);
  const [fileProgress, setFileProgress] = useState<FileProgress>({});
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const [confiramationModal, setConfiramationModal] = useState<boolean>(false);
  const [otpVerificationToken, setOtpVerificationToken] =
    useState<AppointmentPayload>();

  const { validateFileSize, validateFileType } = useFileValidation();
  const { notify } = useNotificationContext();
  const { postData } = usePostApi("");

  const searchParams = useSearchParams();
  const appointmentId = searchParams.get("appointmentId") || "";
  // const customerName = searchParams?.get("name") || "";
  // const eventname = searchParams?.get("eventName") || "";

  // ✅ Only replace spaces if a `+` sign is missing
  // const fixTimezoneOffset = (dateTime: string) => {
  //   if (dateTime?.includes(" ")) return dateTime?.replace(" ", "+");
  //   return dateTime;
  // };

  // const startTimeISO = fixTimezoneOffset(
  //   decodeURIComponent(searchParams?.get("startTime") || "")
  // );
  // const endTimeISO = fixTimezoneOffset(
  //   decodeURIComponent(searchParams?.get("endTime") || "")
  // );

  // // Parse and format start and end dates
  // const timezoneOffset = getTimezoneOffsetFromDateTimeString(startTimeISO);

  // const startDate = getFormatedDate({
  //   date: dayjs(startTimeISO),
  //   tz: timezoneOffset,
  // });

  // const endDate = getFormatedDate({
  //   date: dayjs(endTimeISO),
  //   tz: timezoneOffset,
  // });

  // const startTime = getFormatedTime({
  //   date: dayjs(startTimeISO),
  //   tz: timezoneOffset,
  // });

  // const endTime = getFormatedTime({
  //   date: dayjs(endTimeISO),
  //   tz: timezoneOffset,
  // });

  //get all uploaded documents list
  const { postData: postDocumentsData, data: documentsResponseData } =
    usePostApi("");

  useEffect(() => {
    if (appointmentId) {
      const fetchData = async () => {
        try {
          await postDocumentsData(
            {
              appointmentId: appointmentId,
              searchCriterias: {
                search: "",
              },
            },
            API_CONSTANTS?.documents?.get
          );
        } catch (error) {
          console.error("Error fetching data:", error);
        }
      };
      fetchData();
    }
  }, [appointmentId, postDocumentsData, confiramationModal]);

  const updateFileProgress = (fileName: string, progress: number) => {
    setFileProgress((prev) => ({
      ...prev,
      [fileName]: {
        progress,
        status: progress === 100 ? "completed" : "uploading",
      },
    }));
  };

  const handleDragOver = useCallback((e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
  }, []);

  // Utility function to validate file size
  // const MIN_FILE_SIZE = 1024 * 100; // 100 KB
  // const MAX_FILE_SIZE = 1024 * 1024 * 2; // 2 MB

  const processFiles = (newFiles: File[]) => {
    const validatedFiles = newFiles.filter((file) => {
      return validateFileType(file) && validateFileSize(file);
    });

    const processedFiles = validatedFiles?.map((file) => {
      // Initialize progress for new file
      updateFileProgress(file.name, 0);

      // Simulate upload progress
      simulateFileUpload(file.name);

      return file as FileWithPreview;
    });

    setFiles((prev) => [...prev, ...processedFiles]);
  };

  const simulateFileUpload = (fileName: string) => {
    let progress = 0;
    const interval = setInterval(() => {
      progress += Math.random() * 20;
      if (progress > 100) {
        progress = 100;
        clearInterval(interval);
      }
      updateFileProgress(fileName, Math.min(progress, 100));
    }, 500);
  };

  const handleDrop = useCallback((e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
    const droppedFiles = Array?.from(e.dataTransfer.files);
    processFiles(droppedFiles);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleFileInput = (e: ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const selectedFiles = Array.from(e.target.files);
      processFiles(selectedFiles);
    }

    // Reset the file input after selection to allow re-selection
    e.target.value = "";
  };

  const removeFile = (fileName: string) => {
    setFiles((prev) => prev.filter((file) => file.name !== fileName));
    setFileProgress((prev) => {
      const newProgress = { ...prev };
      delete newProgress[fileName];
      return newProgress;
    });
  };

  // Function to upload the files via API
  const uploadFiles = async () => {
    if (files?.length === 0) return;
    setIsUploading(true);
    const formData = new FormData();
    files.forEach((file) => {
      formData.append("files", file); // Add each file to FormData
    });

    formData.append("appointmentId", appointmentId);

    try {
      if (appointmentId) {
        const response = await postData(
          formData,
          API_CONSTANTS?.uploadDetails?.save,
          otpVerificationToken?.token
        );

        if (response?.status === 200) {
          setConfiramationModal(true);
          setFiles([]);
          setFileProgress({});
        } else {
          setConfiramationModal(false);
          notify(response?.response?.data?.message, "error");
        }
      }
    } catch (error) {
      if (error instanceof Error) {
        notify(error.message, "error");
      } else {
        notify("An unknown error occurred", "error");
      }
    } finally {
      setIsUploading(false);
    }
  };

  const cancelUpload = () => {
    setFiles([]);
    setFileProgress({});
    setIsUploading(false);
  };

  // for disabled button
  const checkAllFilesCompleted = () => {
    return Object?.values(fileProgress)?.every(
      (file) => file?.progress === 100 && file?.status === "completed"
    );
  };

  const closeModal = () => {
    setConfiramationModal(false);
    // router.push("/");
  };

  const renderRow = (label: string, value: string | null) => (
    <div className="flex justify-between gap-x-4 p-1">
      <span className="capitalize font-medium">{label}</span>
      <span>{value !== null && value !== "" ? String(value) : "N/A"}</span>
    </div>
  );

  return (
    <div
      className={`${
        appointmentId ? "h-full" : "min-h-[90vh] flex"
      } bg-white pb-8 md:pt-28`}
    >
      <div
        className={`p-2 ${!appointmentId ? "flex justify-center w-full" : ""}`}
      >
        {/* welcome section */}
        {appointmentId && (
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-1 md:h-[600px] rounded-lg shadow-xl pb-1">
            <div className="col-span-1 p-2 overflow-y-auto">
              {appointmentId && (
                <div className="px-4 py-3  mb-4 text-center lg:text-left">
                  <h1>
                    <strong className="text-xl md:text-2xl italic font-medium text-slate-500">
                      Welcome,{" "}
                    </strong>
                    <strong className="font-dancing text-2xl md:text-3xl text-primary">
                      {otpVerificationToken?.appointmentData?.client?.name ||
                        ""}
                    </strong>
                  </h1>

                  <div className="mt-4">
                    <strong className="text-slate-600">
                      Your Appointment Details:
                    </strong>
                    <div className="text-sm mt-1">
                      {/* {eventname && (
                <p className="text-slate-600"> Event Name: {eventname || ""}</p>
              )} */}

                      {otpVerificationToken?.appointmentData?.date && (
                        <p className="text-slate-600 mb-2">
                          {" "}
                          <span>
                            <span className="font-medium">Date/Time:</span>{" "}
                            {otpVerificationToken?.appointmentData?.date || ""}
                          </span>
                          <span>
                            {" "}
                            {otpVerificationToken?.appointmentData?.startTime ||
                              ""}
                          </span>
                          <span>
                            {" "}
                            (
                            {otpVerificationToken?.appointmentData?.timezone ||
                              ""}
                            )
                          </span>
                        </p>
                      )}
                      {renderRow(
                        "Owner",
                        otpVerificationToken?.appointmentData?.client?.name ||
                          ""
                      )}
                      {renderRow(
                        "Patient",
                        otpVerificationToken?.appointmentData?.pet?.petName ||
                          ""
                      )}
                      {renderRow(
                        "Age",
                        otpVerificationToken?.appointmentData?.pet?.petAge || ""
                      )}
                      {renderRow(
                        "Species",
                        otpVerificationToken?.appointmentData?.pet?.petType ||
                          ""
                      )}
                      {renderRow(
                        "Breed",
                        otpVerificationToken?.appointmentData?.pet?.breed || ""
                      )}
                      {renderRow(
                        "Reason",
                        otpVerificationToken?.appointmentData?.reason === null
                          ? "General Checkup"
                          : otpVerificationToken?.appointmentData?.reason || ""
                      )}
                      {/* {renderRow(
                    "Appointment Type",
                    otpVerificationToken?.appointmentData?.appointmentType === "IN_HOSPITAL"
                      ? "Hospital Visit"
                      : "Virtual Visit"
                  )} */}

                      <div className="flex items-center justify-center mt-4">
                        <LinkButton
                          navigateLink={`${otpVerificationToken?.appointmentData?.meetUrl}`}
                          target="_blank"
                          className="block text-center w-full xl:w-64 mt-4 hover:text-white"
                          label="Join Video"
                        ></LinkButton>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Middle column - 50% */}
            <div className="col-span-2 p-2 overflow-y-auto">
              {appointmentId && (
                <div
                  className={`container grid grid-cols-1 md:grid-cols-1 gap-2 mx-auto`}
                >
                  {/* Upload Section */}
                  {!files?.length && (
                    <div className="col-span-3 bg-white rounded-2xl shadow-md py-8 px-4">
                      <h1 className="text-3xl md:text-4xl font-bold text-primary mb-2 font-dancing text-center">
                        Upload Documents
                      </h1>
                      <p className="italic text-center">
                        Please upload your pet&apos;s documents/pictures which
                        would help to Doctor during virtual consultation.
                      </p>

                      {/* Main upload area */}
                      <div
                        onDragOver={handleDragOver}
                        onDragLeave={handleDragLeave}
                        onDrop={handleDrop}
                        className={`
                mt-8 py-12 px-6 border-2 border-dashed rounded-xl text-center
                transition-all duration-300 ease-in-out
                flex flex-col items-center justify-center
                min-h-[300px] relative
                ${
                  isDragging
                    ? "border-blue-500 bg-blue-50"
                    : "border-gray-200 hover:border-gray-300 bg-gray-50"
                }
              `}
                      >
                        <div className="bg-white p-6 rounded-full shadow-md mb-6">
                          <Upload className="w-8 h-8 text-blue-500" />
                        </div>

                        <h2 className="text-xl font-medium text-gray-700 mb-2">
                          Drag and drop your files here
                        </h2>
                        <p className="text-gray-500 mb-6">or</p>

                        <label className="cursor-pointer">
                          <input
                            type="file"
                            multiple
                            onChange={handleFileInput}
                            className="hidden"
                            accept=".pdf,.doc,.docx,.jpg,.png,.svg"
                          />
                          <span className="bg-primary hover:bg-sky-500 text-white px-6 py-3 rounded-lg transition-colors duration-300 flex items-center group">
                            Browse Files
                            <ArrowRight className="ml-2 w-4 h-4 transform group-hover:translate-x-1 transition-transform duration-300" />
                          </span>
                        </label>

                        <p className="text-sm text-gray-400 mt-6">
                          Supported formats: pdf, xlsx, doc, docx, jpg, jpeg,
                          txt, png, webp
                        </p>
                      </div>
                    </div>
                  )}

                  {/* Preview Section */}
                  {files?.length > 0 && (
                    <div className="col-span-3 bg-white rounded-2xl shadow-xl py-8 px-4">
                      {/* Preview area */}
                      <div className="mt-0">
                        <h3 className="text-lg font-medium text-slate-500 mb-4">
                          Files
                        </h3>
                        <div className="space-y-3 overflow-auto h-[46vh]">
                          {files.map((file, index) => (
                            <div
                              key={index}
                              className="bg-gray-50 rounded-lg p-4 flex flex-col sm:flex-row items-center justify-between group"
                            >
                              <div className="flex items-center">
                                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                  <Upload className="w-5 h-5 text-blue-500" />
                                </div>
                                <div className="ml-4">
                                  <p className="text-sm font-medium text-gray-700">
                                    {file.name}
                                  </p>
                                  {/* <p className="text-xs text-gray-400">
                          {(file.size / 1024 / 1024).toFixed(2)} MB
                        </p> */}
                                </div>
                              </div>
                              <div className="flex items-center space-x-3 mt-4 sm:mt-0">
                                <div className="w-24 bg-gray-200 rounded-full h-1.5">
                                  <div
                                    className="bg-blue-500 h-1.5 rounded-full transition-all duration-300"
                                    style={{
                                      width: `${
                                        fileProgress[file.name]?.progress || 0
                                      }%`,
                                    }}
                                  ></div>
                                </div>
                                <span className="text-sm text-gray-500 w-12">
                                  {Math.round(
                                    fileProgress[file.name]?.progress || 0
                                  )}
                                  %
                                </span>
                                <button
                                  onClick={() => removeFile(file.name)}
                                  disabled={isUploading}
                                >
                                  <X className="w-5 h-5 text-gray-400 hover:text-red-500" />
                                </button>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                      <div className="mt-8 text-center">
                        <button
                          onClick={uploadFiles}
                          className="bg-primary hover:bg-sky-500 text-white px-8 mr-2 py-3 rounded-lg transition-colors duration-300 disabled:opacity-70"
                          disabled={!checkAllFilesCompleted()}
                        >
                          {isUploading && (
                            <div className="inline-block h-3 w-3 mr-2 animate-spin rounded-full border-2 border-solid border-current border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]"></div>
                          )}
                          {isUploading ? "Submitting..." : "Submit"}
                        </button>
                        <button
                          disabled={!checkAllFilesCompleted()}
                          onClick={cancelUpload}
                          className="bg-gray-500 hover:bg-gray-400 text-white px-8 py-3 rounded-lg transition-colors duration-300 disabled:opacity-70"
                        >
                          Cancel
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Right column - 25% */}
            <div className="col-span-1 p-2 overflow-y-auto">
              {documentsResponseData?.body?.length > 0 ? (
                <div className="bg-white border border-gray-200 p-4 space-y-2">
                  <h2 className="text-lg md:text-2xl font-semibold text-primary mb-4 font-dancing">
                    Uploaded Documents
                  </h2>
                  {documentsResponseData.body.map(
                    (
                      doc: {
                        fileName: string;
                        fileUrl: string;
                        fileType: string;
                      },
                      docIndex: number
                    ) => (
                      <div
                        key={docIndex}
                        // className={`relative group w-auto ${
                        //   isDownloadLoading ? "pointer-events-none" : ""
                        // }`}
                      >
                        <button
                          // onClick={() => handleOpenDocument(doc?.id)}
                          className={`w-full folder-container flex flex-row gap-x-2 justify-start items-center  p-4 bg-gray-100 hover:bg-gray-300 rounded-lg shadow-sm transition-all duration-300`}
                        >
                          <div className="flex-shrink-0">
                            {doc?.fileType?.startsWith("image/") ? (
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                viewBox="0 0 48 48"
                                width="24"
                                height="24"
                              >
                                <rect
                                  width="48"
                                  height="48"
                                  rx="4"
                                  fill="#90A4AE"
                                />
                                <circle cx="14" cy="14" r="4" fill="#FFF" />
                                <path
                                  d="M6 36l8-10 6 6 10-12 12 16v4H6v-4z"
                                  fill="#FFF"
                                />
                              </svg>
                            ) : doc?.fileType === "application/pdf" ? (
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                viewBox="0 0 48 48"
                                width="24"
                                height="24"
                              >
                                <path
                                  fill="#D32F2F"
                                  d="M6 4v40h36V14L30 4H6z"
                                />
                                <path fill="#FFCDD2" d="M30 4v10h10L30 4z" />
                                <text
                                  x="24"
                                  y="32"
                                  fontSize="12"
                                  fontWeight="bold"
                                  fill="#FFF"
                                  textAnchor="middle"
                                >
                                  PDF
                                </text>
                              </svg>
                            ) : doc?.fileType === "application/vnd.ms-excel" ||
                              doc?.fileType ===
                                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" ? (
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                viewBox="0 0 48 48"
                                width="24"
                                height="24"
                              >
                                <path
                                  fill="#388E3C"
                                  d="M6 4v40h36V14L30 4H6z"
                                />
                                <path fill="#C8E6C9" d="M30 4v10h10L30 4z" />
                                <text
                                  x="24"
                                  y="32"
                                  fontSize="12"
                                  fontWeight="bold"
                                  fill="#FFF"
                                  textAnchor="middle"
                                >
                                  XLS
                                </text>
                              </svg>
                            ) : doc?.fileType === "application/msword" ||
                              doc?.fileType ===
                                "application/vnd.openxmlformats-officedocument.wordprocessingml.document" ? (
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                viewBox="0 0 48 48"
                                width="24"
                                height="24"
                              >
                                <path
                                  fill="#1976D2"
                                  d="M6 4v40h36V14L30 4H6z"
                                />
                                <path fill="#BBDEFB" d="M30 4v10h10L30 4z" />
                                <text
                                  x="24"
                                  y="32"
                                  fontSize="12"
                                  fontWeight="bold"
                                  fill="#FFF"
                                  textAnchor="middle"
                                >
                                  DOC
                                </text>
                              </svg>
                            ) : (
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                viewBox="0 0 48 48"
                                width="24"
                                height="24"
                              >
                                <path
                                  fill="#90A4AE"
                                  d="M6 4v40h36V14L30 4H6z"
                                />
                                <path fill="#CFD8DC" d="M30 4v10h10L30 4z" />
                                <path
                                  fill="#FFF"
                                  d="M14 36h20v-2H14v2zm0-4h20v-2H14v2zm0-4h20v-2H14v2zm0-4h20v-2H14v2zm0-4h10v-2H14v2z"
                                />
                              </svg>
                            )}
                          </div>
                          <span
                            className={`folder-name break-words text-start text-[12px] sm:text-sm`}
                            title={doc?.fileName}
                          >
                            {doc?.fileName}
                          </span>
                        </button>
                        <div className="absolute p-1 flex items-center justify-end top-0 right-0 opacity-0 group-hover:opacity-100 gap-1 transition-opacity duration-300">
                          <button
                            title="Download"
                            // onClick={() => handleDownloadDocument(doc?.id)}
                          >
                            <Download
                              size={24}
                              className="text-[#0175D4] bg-white hover:bg-slate-200 rounded-full p-1"
                            />
                          </button>
                          {/* <button
                                title="Delete"
                                onClick={() => handleDeleteClick(doc?.id)}
                              >
                                <Trash2Icon
                                  size={24}
                                  className="text-red-500 bg-white hover:bg-slate-200 rounded-full p-1"
                                />
                              </button> */}
                        </div>
                      </div>
                    )
                  )}
                </div>
              ) : (
                <div className="flex items-center justify-center h-full text-center">
                  No uploaded Document
                </div>
              )}
            </div>
          </div>
        )}

        {!appointmentId && (
          <div className="container m-auto flex flex-col items-center justify-center p-4 text-center">
            <h2 className="text-3xl md:text-4xl font-dancing text-primary">
              Appointment First, Upload Next
            </h2>
            <p className="my-4">
              Please visit to home page to take an appointment.
            </p>
            <LinkButton navigateLink="/" label="Home Page"></LinkButton>
          </div>
        )}

        {/* documents list */}
        {/* <div className="col-span-1 p-2">
          <div className="bg-white border border-gray-200 rounded-lg p-4 space-y-2">
            <h2 className="text-lg font-semibold text-primary mb-2">Uploaded Documents</h2>

            {files?.length > 0 ? (
              files.map((file, index) => (
                <div
                  key={index}
                  className="bg-gray-50 border border-gray-200 p-2 rounded-md hover:bg-gray-100 transition-all duration-200"
                >
                  {file.name}
                </div>
              ))
            ) : (
              <p className="text-gray-500">No documents uploaded yet.</p>
            )}
          </div>

        </div> */}
      </div>

      {confiramationModal && (
        <div className="fixed inset-0 px-4 flex flex-wrap justify-center items-center w-full h-full z-[1000] before:fixed before:inset-0 before:w-full before:h-full before:bg-[rgba(0,0,0,0.5)] overflow-auto font-[sans-serif]">
          <div className="w-full max-w-md bg-white shadow-lg rounded-lg p-6 relative mx-auto text-center">
            <span
              className="absolute right-3 top-2 cursor-pointer p-1 hover:bg-slate-200 rounded-full"
              onClick={closeModal}
            >
              <X size={18} color="grey" />
            </span>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="w-20 h-20 fill-green-500 absolute left-1/2 top-0 -translate-x-1/2 -translate-y-1/2"
              viewBox="0 0 60 60"
            >
              <circle cx="30" cy="30" r="29" data-original="#5edd60" />
              <path
                fill="#fff"
                d="m24.262 42.07-6.8-6.642a1.534 1.534 0 0 1 0-2.2l2.255-2.2a1.621 1.621 0 0 1 2.256 0l4.048 3.957 11.353-17.26a1.617 1.617 0 0 1 2.2-.468l2.684 1.686a1.537 1.537 0 0 1 .479 2.154L29.294 41.541a3.3 3.3 0 0 1-5.032.529z"
                data-original="#ffffff"
              />
            </svg>

            <div className="mt-12">
              <h3 className="text-gray-800 text-2xl font-bold flex-1">
                Awesome!
              </h3>
              <p className="text-sm text-gray-400 mt-3">
                Your files have been successfully submitted
              </p>
              {/* <p className="font-dancing font-semibold text-xl mt-3">
                Thank You Visit Again!!
              </p> */}

              <button
                type="button"
                onClick={closeModal}
                className="px-6 py-2.5 mt-8 w-full rounded-md text-white text-sm font-semibold tracking-wide border-none outline-none bg-green-500 hover:bg-green-600"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}

      <OTPVerification
        setOtpVerificationToken={(tokenValue: AppointmentPayload) =>
          setOtpVerificationToken(tokenValue)
        }
      />
    </div>
  );
};

export default UploadDetails;
