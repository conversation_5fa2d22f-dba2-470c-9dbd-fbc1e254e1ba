/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { API_CONSTANTS } from "@/app/constant/api-constants/apiConfig";
import { countryPhoneCodes } from "@/app/constant/constant";
import { useNotificationContext } from "@/app/context/NotificationContext";
import { useGetApi, usePostApi } from "@/app/services/useApi";
import { Button } from "@/app/components/ui/button";
import { Calendar } from "@/app/components/ui/calendar";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/app/components/ui/card";
import { Input } from "@/app/components/ui/input";
import { Label } from "@/app/components/ui/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/app/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/app/components/ui/select";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/app/components/ui/sheet";
import { Textarea } from "@/app/components/ui/textarea";
import { cn } from "@/app/lib/utils";
import { format } from "date-fns";
import dayjs from "dayjs";
import customParseFormat from "dayjs/plugin/customParseFormat";
import isSameOrBefore from "dayjs/plugin/isSameOrBefore";
import timezone from "dayjs/plugin/timezone";
import utc from "dayjs/plugin/utc";
import {
  Building2,
  CalendarIcon,
  CheckCircle,
  ChevronDown,
  DollarSign,
  Heart,
  Info,
  PawPrint,
  Phone,
  Stethoscope,
  User,
  Video,
} from "lucide-react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useEffect, useMemo, useState } from "react";
import ReCAPTCHA from "react-google-recaptcha";

dayjs.extend(isSameOrBefore);
dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(customParseFormat);

type FormErrors = {
  customerName?: string;
  phone?: string;
  petName?: string;
  petType?: string;
  reason?: string;
  petGender?: string;
  appointmentDate?: string;
  appointmentTime?: string;
  captchaError?: string;
};

// Mock data
const appointmentTypes = [
  {
    id: "IN_HOSPITAL",
    name: "In-Hospital Visit",
    description: "Comprehensive examination at our facility",
    price: 0,
    duration: "45-60 min",
    icon: Building2,
    popular: true,
    features: [
      "Full examination",
      "Lab tests available",
      "Immediate treatment",
    ],
  },
  {
    id: "TELE_MEDICINE",
    name: "Tele-Medicine Consultation",
    description: "Virtual consultation with our veterinarians",
    price: 29,
    duration: "20-30 min",
    icon: Video,
    popular: false,
    features: ["Video consultation", "Follow-up care"],
  },
];

interface FormData {
  customerName: string;
  phone: string;
  petName: string;
  petType: string;
  petGender: string;
  appointmentDate: Date | undefined;
  appointmentTime: string;
  reason: string;
  timeZone: string;
  apptType: string;
}

export default function BookAppointmentPage() {
  const router = useRouter();
  const [selectedCountry] = useState(countryPhoneCodes[0]); // Default to first
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showMobileSummary, setShowMobileSummary] = useState(false);
  const [showConfirmationModal, setShowConfirmationModal] = useState(false);
  const [timeSlots, setTimeSlots] = useState<string[]>([]);
  const [captchaToken, setCaptchaToken] = useState("");
  const { notify } = useNotificationContext();
  const [currentMonth, setCurrentMonth] = useState<number>(
    new Date().getMonth()
  );
  const [currentYear, setCurrentYear] = useState<number>(
    new Date().getFullYear()
  );

  const [formErrors, setFormErrors] = useState<FormErrors>({});

  const [selectedDate, setSelectedDate] = useState<string | null>(
    `${dayjs().tz("America/Los_Angeles").date()}`
  );
  const formattedMonth = String(currentMonth + 1).padStart(2, "0");

  // const CalendarID = "2acb3346-6c60-45ed-8bb0-b9da4f702b7a";
  // const ProviderID = "606b5bbf-abe2-4095-a12b-609b8c0d52e8";
  const CalendarID = "61acc1b1-e7e0-405c-ad2e-66cb86ecc60a";
  const ProviderID = "20d46a2c-ba2f-439b-a879-3b583976db07";

  // check appointment(user) availability
  const { getData, data } = useGetApi("");

  // book appointment
  const { postData: bookAppointment } = usePostApi(
    API_CONSTANTS?.bookAppointment?.saveWithCaptcha
  );

  // payment api
  const { postData: postPayment } = usePostApi(
    `${API_CONSTANTS?.payment?.save}`
  );

  const initialFormData = useMemo(
    () => ({
      customerName: "",
      phone: "",
      petName: "",
      petType: "",
      petGender: "",
      appointmentDate: undefined,
      appointmentTime: "",
      reason: "",
      timeZone: "",
      apptType: "IN_HOSPITAL",
    }),
    []
  );

  const [isDatePickerOpen, setIsDatePickerOpen] = useState(false);
  const [formData, setFormData] = useState<FormData>(initialFormData);
  const cleanedPhone = formData?.phone?.replace(/[()\s-]/g, "");

  const updateFormData = (field: keyof FormData, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const selectedAppointmentType = appointmentTypes.find(
    (type) => type.id === formData.apptType
  );

  // const isFormValid = () => {
  //   return (
  //     formData.apptType &&
  //     formData.customerName &&
  //     formData.phone &&
  //     formData.petName &&
  //     formData.petType &&
  //     formData.petGender &&
  //     formData.appointmentDate &&
  //     formData.appointmentTime &&
  //     formData.reason
  //   );
  // };

  const userCheckAvailability = async (props: {
    calendarId: string;
    formattedMonth: string;
    currentYear: number;
  }) => {
    const response = await getData(
      `${API_CONSTANTS?.bookAppointment?.checkUserAvailability}?calendarId=${props?.calendarId}&month=${props?.formattedMonth}&year=${props?.currentYear}`
    );

    if (
      response?.status === 200 &&
      response?.data?.body?.availability?.length
    ) {
      const timezone = response?.data?.body?.timezone || "America/Los_Angeles";
      setFormData((prev) => ({
        ...prev,
        timeZone: timezone,
      }));
    } else if (response?.status === 401) {
      notify(response?.response?.data?.message, "error");
    } else {
      notify(response?.response?.data?.message, "error");
    }
  };

  useEffect(() => {
    userCheckAvailability({
      calendarId: CalendarID,
      formattedMonth,
      currentYear,
    });
  }, [currentMonth, currentYear]);

  const userTimezone = data?.body?.timezone ?? "America/Los_Angeles"; // fallback just in case
  const [todayInUserTZ, maxDateInUserTZ] = useMemo(() => {
    const today = dayjs().tz(userTimezone).startOf("day");
    const max = today.add(60, "day").endOf("day");
    return [today, max];
  }, [userTimezone]);

  const resetForm = () => {
    setCaptchaToken("");
    setFormData({
      ...initialFormData,
      // appointmentDate: dayjs().tz(userTimezone).startOf("day").toDate(),
    });
    // iframe closed then availability slot call
    userCheckAvailability({
      calendarId: CalendarID,
      formattedMonth,
      currentYear,
    });
  };

  const handleDateSelect = (date: Date | undefined) => {
    if (!date) {
      setIsDatePickerOpen(false);
      return;
    }

    updateFormData("appointmentTime", "");
    // Format date directly as YYYY-MM-DD (local date, no tz shift)
    const formattedDate = dayjs(date).format("YYYY-MM-DD");

    // Set string to formData instead of JS Date object
    // setFormData((prev) => ({
    //   ...prev,
    //   appointmentDate: formattedDate,
    // }));
    const currentDateFormatted = formData.appointmentDate
      ? dayjs(formData.appointmentDate).format("YYYY-MM-DD")
      : "";

    if (formattedDate === currentDateFormatted) {
      setIsDatePickerOpen(false);
      return;
    }

    setIsDatePickerOpen(false);
    updateFormData("appointmentDate", formattedDate);
    const selectedDay = dayjs(date).format("DD");
    setSelectedDate(selectedDay);
    setCurrentMonth(Number(dayjs(date).format("MM")) - 1); // Months are 0-indexed
    setCurrentYear(Number(dayjs(date).format("YYYY")));
  };

  function generateTimeSlots(
    start: string,
    end: string,
    duration: number
  ): string[] {
    const startTime = dayjs(`2023-01-01T${start}`);
    const endTime = dayjs(`2023-01-01T${end}`);
    const slots: string[] = [];

    let current = startTime;
    while (current.add(duration, "minute").isSameOrBefore(endTime)) {
      const slot = current.format("HH:mm");
      slots.push(slot);
      current = current.add(duration, "minute");
    }
    return slots;
  }

  useEffect(() => {
    updateFormData("appointmentTime", "");

    const dateObj = new Date(
      `${currentYear}-${String(currentMonth + 1).padStart(2, "0")}-${String(
        selectedDate
      ).padStart(2, "0")}`
    );

    const dayOfWeek = dateObj.getDay(); // 0-6 (Sun-Sat)

    const availability = data?.body?.availability.find(
      (a: { dayOfWeek: number }) => a.dayOfWeek === dayOfWeek
    );

    if (!availability) {
      setTimeSlots([]); // No slots if no availability
      return;
    }

    const { startTime, endTime } = availability;

    const slotDuration = data?.body?.slotDuration || 30;

    const allSlots = generateTimeSlots(startTime, endTime, slotDuration);

    const availableSlots = allSlots
      .filter(
        (slot) =>
          !isSlotBooked(
            slot,
            new Date(
              `${currentYear}-${String(currentMonth + 1).padStart(
                2,
                "0"
              )}-${String(selectedDate).padStart(2, "0")}`
            ),
            data?.body?.bookedSlots
          )
      )
      .map((slot) => {
        // Combine date + time, convert to correct timezone
        const slotDateTime = dayjs.tz(
          `${currentYear}-${String(currentMonth + 1).padStart(2, "0")}-${String(
            selectedDate
          ).padStart(2, "0")}T${slot}`,
          userTimezone
        );

        return slotDateTime.format("h:mm A");
      });

    setTimeSlots(availableSlots);
  }, [selectedDate, data]);

  function isSlotBooked(
    slotTime: string,
    selectedDate: Date,
    bookedSlots: any[]
  ): boolean {
    const slotStart = dayjs(selectedDate)
      .hour(Number(slotTime.split(":")[0]))
      .minute(Number(slotTime.split(":")[1]));
    const slotEnd = slotStart.add(data?.body?.slotDuration, "minute");

    return bookedSlots.some((booked) => {
      const bookedStart = dayjs
        .utc(booked.startDateTime)
        .tz(data?.body?.timezone)
        ?.format("YYYY-MM-DD HH:mm:ss");
      const bookedEnd = dayjs
        .utc(booked.endDateTime)
        .tz(data?.body?.timezone)
        ?.format("YYYY-MM-DD HH:mm:ss");

      return (
        slotStart.isSame(bookedStart, "minute") ||
        (slotStart.isAfter(bookedStart) && slotStart.isBefore(bookedEnd)) ||
        (slotEnd.isAfter(bookedStart) && slotEnd.isBefore(bookedEnd)) ||
        (slotStart.isBefore(bookedStart) && slotEnd.isAfter(bookedEnd))
      );
    });
  }

  // useEffect(() => {
  //   if (!formData.appointmentDate && userTimezone) {
  //     const todayInPST = dayjs().tz(userTimezone).startOf("day");
  //     updateFormData("appointmentDate", todayInPST.toDate());
  //   }
  // }, [userTimezone]);

  const handleCaptchaChange = (value: any) => {
    if (value) {
      setCaptchaToken(value);
    } else {
      setCaptchaToken("");
    }
  };

  // Helper function to sanitize and format time
  const sanitizeAndFormatTime = (timeStr: string) => {
    if (!timeStr) return "";

    // Clean up unwanted AM/PM with 24-hour format
    const cleaned = timeStr
      .replace(/\s?(AM|PM)/gi, "") // Remove AM/PM if 24-hr
      .trim();

    // Try parsing as 12-hour with AM/PM or 24-hour format
    const parsed = dayjs(timeStr, "h:mm A", true).isValid()
      ? dayjs(timeStr, "h:mm A", true)
      : dayjs(cleaned, "HH:mm", true);

    return parsed.isValid() ? parsed.format("HH:mm") : "";
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);

    const modifiedSubmitData = {
      ...formData,
      calendarId: CalendarID,
      phone: `${selectedCountry?.dialCode}${cleanedPhone}`,
      appointmentDate: dayjs(formData.appointmentDate).format("YYYY-MM-DD"),
      providerId: ProviderID,
      appointmentTime: sanitizeAndFormatTime(formData.appointmentTime),
      captchaToken: captchaToken,
    };

    const response = await bookAppointment(modifiedSubmitData);

    if (response?.status === 201) {
      setCaptchaToken("");

      if (formData.apptType === "TELE_MEDICINE") {
        // Proceed with payment
        const paymetResponse = await postPayment({
          slotId: response?.data?.body?.slotId,
          calendarId: response?.data?.body?.calendarId,
          provider_id: ProviderID,
        });

        if (paymetResponse?.status === 200) {
          const paymentWindow = window.open(
            paymetResponse?.data?.url,
            "_blank"
          );

          const checkClosedInterval = setInterval(() => {
            if (paymentWindow?.closed) {
              clearInterval(checkClosedInterval);
              resetForm();
            }
          }, 500);
        }
      } else {
        notify(response?.data?.message, "success");
        router?.push("/booking-success"); //if remove router push then reset form state
      }
    } else if (response?.status === 401) {
      notify(response?.response?.data?.message, "error");
    } else {
      notify(response?.response?.data?.message, "error");
    }
    setIsSubmitting(false);
    // Final step: Book appointment
    // await bookAppointmentAPI(formData);
  };

  const handleFormSubmission = () => {
    const errors: FormErrors = {};
    if (!formData.customerName) errors.customerName = "Required";
    if (!formData.phone) {
      errors.phone = "Required";
    } else if (cleanedPhone.length !== 10) {
      errors.phone = "Phone Number must be exactly 10 digits";
    }

    if (!formData.petName) errors.petName = "Required";
    if (!formData.petType) errors.petType = "Required";
    if (!formData.reason) errors.reason = "Required";
    if (!formData.petGender) errors.petGender = "Required";
    if (!formData.appointmentDate) {
      errors.appointmentDate = "Required";
    }

    if (!formData.appointmentTime) {
      errors.appointmentTime = "Required";
    }

    if (captchaToken === "") {
      errors.captchaError = "Required";
    }

    setFormErrors(errors);

    if (Object.keys(errors)?.length === 0) {
      setShowConfirmationModal(true);
      // if (formData.apptType === "TELE_MEDICINE") {
      //   setShowConfirmationModal(true);
      // } else {
      //   handleSubmit();
      // }
    }
  };

  // Summary Component for reuse
  const AppointmentSummary = ({ className = "" }: { className?: string }) => (
    <Card className={`shadow-lg border-0 ${className}`}>
      <CardHeader className="bg-gradient-to-r from-blue-500 to-[#0075d4] text-white rounded-t-lg">
        <CardTitle className="flex items-center gap-2 text-lg">
          <CheckCircle className="h-5 w-5" />
          Appointment Summary
        </CardTitle>
      </CardHeader>
      <CardContent className="p-4 sm:p-6 space-y-4">
        {selectedAppointmentType && (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="font-medium text-sm sm:text-base">Service</span>
              <span className="text-xs sm:text-sm text-gray-600 text-right max-w-[60%]">
                {selectedAppointmentType.name}
              </span>
            </div>

            <div className="flex items-center justify-between">
              <span className="font-medium text-sm sm:text-base">Duration</span>
              <span className="text-xs sm:text-sm text-gray-600">
                {selectedAppointmentType.duration}
              </span>
            </div>

            {selectedAppointmentType.id === "TELE_MEDICINE" && (
              <div className="flex items-center justify-between">
                <span className="font-medium text-sm sm:text-base">Cost</span>
                <span className="text-lg font-bold text-green-600">
                  ${selectedAppointmentType.price}
                </span>
              </div>
            )}
          </div>
        )}

        {formData.appointmentDate && (
          <div className="flex items-center justify-between pt-3 border-t">
            <span className="font-medium text-sm sm:text-base">Date</span>
            <span className="text-xs sm:text-sm text-gray-600">
              {format(formData?.appointmentDate, "MM/dd/yyyy")}
            </span>
          </div>
        )}

        {formData.appointmentTime && (
          <div className="flex items-center justify-between">
            <span className="font-medium text-sm sm:text-base">Time</span>
            <span className="text-xs sm:text-sm text-gray-600">
              {formData.appointmentTime}
            </span>
          </div>
        )}

        {formData.petName && (
          <div className="flex items-center justify-between">
            <span className="font-medium text-sm sm:text-base">Pet</span>
            <span className="text-xs sm:text-sm text-gray-600">
              {formData.petName}
            </span>
          </div>
        )}
      </CardContent>
    </Card>
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 pt-5">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-8 sm:pb-16">
        <form onSubmit={(e) => e.preventDefault()}>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8">
            {/* Main Form */}
            <div className="lg:col-span-2 space-y-3">
              {/* Appointment Type Selection */}
              <Card className="shadow-lg border-0">
                <Card className="border-0 bg-gradient-to-r from-blue-500 to-[#0075d4] text-white rounded-b-none">
                  <CardHeader className="px-4 sm:px-6 py-4">
                    <CardTitle className="text-lg sm:text-2xl flex items-center gap-2">
                      <PawPrint className="h-10 w-10 text-white p-2  rounded-full" />
                      Book Your Appointment
                    </CardTitle>
                  </CardHeader>
                </Card>
                {/* Mobile Summary Button */}
                <div className="lg:hidden mb-3">
                  <Sheet
                    open={showMobileSummary}
                    onOpenChange={setShowMobileSummary}
                  >
                    <SheetTrigger asChild>
                      <Button
                        variant="outline"
                        className="w-full h-12 bg-white shadow-md rounded-none border-gray-200 flex items-center justify-between"
                      >
                        <div className="flex items-center gap-2">
                          <Info className="h-4 w-4" />
                          <span>View Summary</span>
                        </div>
                        <div className="flex items-center gap-2">
                          {selectedAppointmentType?.id === "TELE_MEDICINE" && (
                            <span className="font-bold text-green-600">
                              ${selectedAppointmentType.price}
                            </span>
                          )}
                          <ChevronDown className="h-4 w-4" />
                        </div>
                      </Button>
                    </SheetTrigger>
                    <SheetContent side="bottom" className="h-[60vh]">
                      <SheetHeader>
                        <SheetTitle>Appointment Summary</SheetTitle>
                      </SheetHeader>
                      <div className="mt-6 space-y-6">
                        <AppointmentSummary />
                      </div>
                    </SheetContent>
                  </Sheet>
                </div>
                <CardHeader className="pb-3 px-4 sm:px-6">
                  <CardTitle className="flex items-center gap-2 text-md">
                    <Stethoscope className="h-5 w-5 text-primary" />
                    Choose Your Appointment Type
                  </CardTitle>
                  <CardDescription className="text-sm">
                    Select the type of care your pet needs
                  </CardDescription>
                </CardHeader>
                <CardContent className="p-4 sm:p-6 sm:!pt-2">
                  <div className="grid grid-cols-1 sm:grid-cols-2  gap-3 sm:gap-4">
                    {appointmentTypes.map((type) => {
                      const IconComponent = type.icon;
                      return (
                        <div
                          key={type.id}
                          className={`relative p-4 border-2 rounded-xl cursor-pointer transition-all hover:shadow-md ${
                            formData.apptType === type.id
                              ? "border-blue-500 bg-blue-50 shadow-md"
                              : "border-gray-200 hover:border-blue-300"
                          }`}
                          onClick={() => updateFormData("apptType", type.id)}
                        >
                          {/* {type.popular && (
                            <Badge className="absolute -top-2 -right-2 bg-orange-500 text-xs">Most Popular</Badge>
                          )} */}

                          <div className=" text-center space-y-2 sm:space-y-3">
                            <div
                              className={`w-10 h-10 mx-auto rounded-full flex items-center justify-center ${
                                formData.apptType === type.id
                                  ? "bg-primary"
                                  : "bg-gray-100"
                              }`}
                            >
                              <IconComponent
                                className={`h-5 w-5 ${
                                  formData.apptType === type.id
                                    ? "text-white"
                                    : "text-gray-600"
                                }`}
                              />
                            </div>

                            <div>
                              <h3 className="flex items-center justify-center font-semibold text-gray-900 text-sm sm:text-base">
                                {type.name}
                                {type.id === "TELE_MEDICINE" && (
                                  <span className="flex items-center bg-yellow-300 text-black font-semibold text-sm px-1 ml-2 rounded-full border border-yellow-700">
                                    <DollarSign className="h-3 w-3" />
                                    {type.price}
                                  </span>
                                )}
                              </h3>
                            </div>
                          </div>
                          <span className="text-black absolute -top-[0.7rem] right-3 bg-yellow-100 border rounded-xl px-2 text-[12px]">
                            {type.duration}
                          </span>
                        </div>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>

              {/* Owner & Pet Information */}
              <Card className="shadow-lg border-0">
                <CardHeader className="pb-3 px-4 sm:px-6">
                  <CardTitle className="flex items-center gap-2 text-md">
                    <User className="h-5 w-5 text-primary" />
                    Owner & Pet Information
                  </CardTitle>
                  <CardDescription className="text-sm">
                    Tell us about you and your pet
                  </CardDescription>
                </CardHeader>
                <CardContent className="p-4 sm:p-6 sm:!pt-2 space-y-4 sm:space-y-6">
                  {/* Owner Information */}
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label
                        htmlFor="customerName"
                        className="flex items-center gap-2 text-sm"
                      >
                        <User className="h-4 w-4 text-blue-500" />
                        Owner Name <span className="text-red-500">*</span>{" "}
                        {formErrors.customerName && (
                          <span className="inline-flex items-center rounded-md bg-red-50 px-2 py-1 text-xs font-medium text-red-700 ring-1 ring-red-600/10 ring-inset">
                            <span className="text-red-500 text-[12px]">
                              {formErrors.customerName}
                            </span>
                          </span>
                        )}
                      </Label>
                      <Input
                        id="customerName"
                        placeholder="Enter Owner Name"
                        value={formData.customerName}
                        onChange={(e) =>
                          updateFormData("customerName", e.target.value)
                        }
                        className="h-10 sm:h-12 border-gray-300 focus:border-blue-500 text-sm sm:text-base"
                        required
                      />
                    </div>

                    <div className="space-y-2">
                      <Label
                        htmlFor="phone"
                        className="flex items-center gap-2 text-sm"
                      >
                        <Phone className="h-4 w-4 text-blue-500" />
                        Phone Number <span className="text-red-500">*</span>
                        {formErrors.phone && (
                          <span className="inline-flex items-center rounded-md bg-red-50 px-2 py-1 text-xs font-medium text-red-700 ring-1 ring-red-600/10 ring-inset">
                            <span className="text-red-500 text-[12px]">
                              {formErrors.phone}
                            </span>
                          </span>
                        )}
                      </Label>
                      <Input
                        id="phone"
                        placeholder="(*************"
                        value={formData.phone}
                        onChange={(e) => {
                          const raw = e.target.value.replace(/\D/g, ""); // Remove non-digits
                          let formatted = raw;

                          if (raw.length > 0) {
                            formatted = `(${raw.slice(0, 3)}`;
                          }
                          if (raw.length >= 4) {
                            formatted += `) ${raw.slice(3, 6)}`;
                          }
                          if (raw.length >= 7) {
                            formatted += `-${raw.slice(6, 10)}`;
                          }

                          updateFormData("phone", formatted);
                        }}
                        className="h-10 sm:h-12 border-gray-300 focus:border-blue-500 text-sm"
                        required
                      />
                    </div>

                    <div className="space-y-2">
                      <Label
                        htmlFor="petName"
                        className="flex items-center gap-2 text-sm"
                      >
                        <PawPrint className="h-4 w-4 text-blue-500" />
                        Pet Name <span className="text-red-500">*</span>
                        {formErrors.petName && (
                          <span className="inline-flex items-center rounded-md bg-red-50 px-2 py-1 text-xs font-medium text-red-700 ring-1 ring-red-600/10 ring-inset">
                            <span className="text-red-500 text-[12px]">
                              {formErrors.petName}
                            </span>
                          </span>
                        )}
                      </Label>
                      <Input
                        id="petName"
                        placeholder="Enter Pet Name"
                        value={formData.petName}
                        onChange={(e) =>
                          updateFormData("petName", e.target.value)
                        }
                        className="h-10 sm:h-12 border-gray-300 focus:border-blue-500 text-sm sm:text-base"
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label className="flex items-center gap-2 text-sm">
                        <Heart className="h-4 w-4 text-blue-500" />
                        Pet Type <span className="text-red-500">*</span>
                        {formErrors.petType && (
                          <span className="inline-flex items-center rounded-md bg-red-50 px-2 py-1 text-xs font-medium text-red-700 ring-1 ring-red-600/10 ring-inset">
                            <span className="text-red-500 text-[12px]">
                              {formErrors.petType}
                            </span>
                          </span>
                        )}
                      </Label>
                      <div className="grid grid-cols-2 gap-2 sm:gap-3">
                        <Button
                          type="button"
                          variant={
                            formData.petType === "CAN" ? "default" : "outline"
                          }
                          size="lg"
                          onClick={() => updateFormData("petType", "CAN")}
                          className={`h-14 sm:h-16 flex flex-col items-center gap-1  transition-all text-sm sm:text-base ${
                            formData.petType === "CAN"
                              ? "bg-primary hover:bg-blue-700 text-white shadow-lg"
                              : "bg-white hover:bg-blue-50 border-2 border-gray-200 hover:border-blue-300"
                          }`}
                        >
                          <span className="text-xl sm:text-2xl">
                            <Image
                              src="/images/dog_profile.png"
                              alt="Dog"
                              width={26}
                              height={26}
                              className="rounded-full object-cover"
                              title="Dog"
                            />
                          </span>
                          <span className="font-medium">Dog</span>
                        </Button>

                        <Button
                          type="button"
                          variant={
                            formData.petType === "FEL" ? "default" : "outline"
                          }
                          size="lg"
                          onClick={() => updateFormData("petType", "FEL")}
                          className={`h-14 sm:h-16 flex flex-col items-center gap-1 transition-all text-sm sm:text-base ${
                            formData.petType === "FEL"
                              ? "bg-primary hover:bg-blue-700 text-white shadow-lg"
                              : "bg-white hover:bg-blue-50 border-2 border-gray-200 hover:border-blue-300"
                          }`}
                        >
                          <span className="text-xl sm:text-2xl">
                            <Image
                              src="/images/cat-8570981.webp"
                              alt="Cat"
                              width={30}
                              height={30}
                              className="rounded-full object-cover"
                              title="Cat"
                            />
                          </span>
                          <span className="font-medium">Cat</span>
                        </Button>
                      </div>
                    </div>
                  </div>

                  {/* Pet Type & Gender */}
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
                    <div className="space-y-3">
                      <Label className="flex items-center gap-2 text-sm">
                        Pet Gender <span className="text-red-500">*</span>
                        {formErrors.petGender && (
                          <span className="inline-flex items-center rounded-md bg-red-50 px-2 py-1 text-xs font-medium text-red-700 ring-1 ring-red-600/10 ring-inset">
                            <span className="text-red-500 text-[12px]">
                              {formErrors.petGender}
                            </span>
                          </span>
                        )}
                      </Label>
                      <div className="grid grid-cols-2 gap-2 sm:gap-3">
                        <Button
                          type="button"
                          variant={
                            formData.petGender === "male"
                              ? "default"
                              : "outline"
                          }
                          size="lg"
                          onClick={() => updateFormData("petGender", "male")}
                          className={`h-14 sm:h-16 transition-all text-sm sm:text-base ${
                            formData.petGender === "male"
                              ? "bg-primary hover:bg-blue-700 text-white shadow-lg"
                              : "bg-white hover:bg-blue-50 border-2 border-gray-200 hover:border-blue-300"
                          }`}
                        >
                          <span className="font-medium">Male</span>
                        </Button>

                        <Button
                          type="button"
                          variant={
                            formData.petGender === "female"
                              ? "default"
                              : "outline"
                          }
                          size="lg"
                          onClick={() => updateFormData("petGender", "female")}
                          className={`h-14 sm:h-16 transition-all text-sm sm:text-base ${
                            formData.petGender === "female"
                              ? "bg-primary hover:bg-blue-700 text-white shadow-lg"
                              : "bg-white hover:bg-blue-50 border-2 border-gray-200 hover:border-blue-300"
                          }`}
                        >
                          <span className="font-medium">Female</span>
                        </Button>
                      </div>
                    </div>
                    {/* Purpose */}
                    <div className="space-y-3">
                      <Label
                        htmlFor="reason"
                        className="flex items-center gap-2 text-sm"
                      >
                        <Stethoscope className="h-4 w-4 text-blue-500" />
                        Purpose of Visit <span className="text-red-500">*</span>
                        {formErrors.reason && (
                          <span className="inline-flex items-center rounded-md bg-red-50 px-2 py-1 text-xs font-medium text-red-700 ring-1 ring-red-600/10 ring-inset">
                            <span className="text-red-500 text-[12px]">
                              {formErrors.reason}
                            </span>
                          </span>
                        )}
                      </Label>
                      <Textarea
                        id="reason"
                        placeholder="Please describe the reason for your visit, symptoms, or concerns..."
                        value={formData.reason}
                        rows={3}
                        onChange={(e) =>
                          updateFormData("reason", e.target.value)
                        }
                        className=" border-gray-300 focus:border-blue-500 text-sm sm:text-base"
                        required
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Date, Time & Veterinarian */}
              <Card className="shadow-lg border-0">
                <CardHeader className="pb-3 px-4 sm:px-6">
                  <CardTitle className="flex items-center gap-2 text-md">
                    <CalendarIcon className="h-5 w-5 text-primary" />
                    Schedule Your Appointment
                  </CardTitle>
                  <CardDescription className="text-sm">
                    Choose your preferred date, time
                  </CardDescription>
                </CardHeader>
                <CardContent className="p-4 sm:p-6 space-y-4 sm:space-y-6 sm:!pt-2">
                  <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label className="flex items-center gap-2 text-sm">
                        Preferred Date <span className="text-red-500">*</span>
                        {formErrors.appointmentDate && (
                          <span className="inline-flex items-center rounded-md bg-red-50 px-2 py-1 text-xs font-medium text-red-700 ring-1 ring-red-600/10 ring-inset">
                            <span className="text-red-500 text-[12px]">
                              {formErrors.appointmentDate}
                            </span>
                          </span>
                        )}
                      </Label>
                      <Popover
                        open={isDatePickerOpen}
                        onOpenChange={setIsDatePickerOpen}
                      >
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            className={cn(
                              "w-full h-10 sm:h-12 justify-start text-left font-normal border-gray-300 text-sm sm:text-base",
                              !formData.appointmentDate &&
                                "text-muted-foreground"
                            )}
                          >
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {formData.appointmentDate ? (
                              format(formData.appointmentDate, "MM/dd/yyyy")
                            ) : (
                              <span>Pick a date</span>
                            )}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          {/* <Calendar
                            mode="single"
                            selected={formData.appointmentDate}
                            onSelect={handleDateSelect}
                            disabled={(date) => {
                              const d = dayjs(date).tz(userTimezone).startOf("day");
                              return d.isBefore(todayInUserTZ) || d.isAfter(maxDateInUserTZ);
                            }}
                            autoFocus
                          /> */}
                          <Calendar
                            mode="single"
                            selected={
                              formData.appointmentDate
                                ? dayjs(formData.appointmentDate)
                                    .tz(userTimezone)
                                    .toDate()
                                : undefined
                            }
                            onSelect={handleDateSelect}
                            disabled={(
                              date:
                                | string
                                | number
                                | dayjs.Dayjs
                                | Date
                                | null
                                | undefined
                            ) => {
                              const localDate = dayjs(date); // From calendar input (probably local)
                              const d = dayjs.tz(
                                localDate.format("YYYY-MM-DD"),
                                userTimezone
                              );
                              return (
                                d.isBefore(todayInUserTZ) ||
                                d.isAfter(maxDateInUserTZ)
                              );
                            }}
                          />
                        </PopoverContent>
                      </Popover>
                    </div>

                    <div className="space-y-2">
                      <Label className="flex items-center gap-2 text-sm">
                        Preferred Time <span className="text-red-500">*</span>
                        {formErrors.appointmentTime && (
                          <span className="inline-flex items-center rounded-md bg-red-50 px-2 py-1 text-xs font-medium text-red-700 ring-1 ring-red-600/10 ring-inset">
                            <span className="text-red-500 text-[12px]">
                              {formErrors.appointmentTime}
                            </span>
                          </span>
                        )}
                      </Label>
                      {/* <Select
                        value={formData.appointmentTime}
                        onValueChange={(value) =>
                          updateFormData("appointmentTime", value)
                        }
                      >
                        <SelectTrigger className="h-10 sm:h-12 border-gray-300 text-sm sm:text-base">
                          <SelectValue placeholder="Select time" />
                        </SelectTrigger>
                        <SelectContent>
                          {timeSlots.map((time) => {
                            return (
                              <SelectItem
                                key={time}
                                value={time}
                                className="text-sm sm:text-base"
                              >
                                {time}
                              </SelectItem>
                            )
                          })}
                        </SelectContent>
                      </Select> */}
                      {(() => {
                        const appointmentDate = dayjs(
                          formData.appointmentDate
                        ).format("YYYY-MM-DD");
                        const now = dayjs().tz(userTimezone);
                        const isToday = dayjs
                          .tz(appointmentDate, "YYYY-MM-DD", userTimezone)
                          .isSame(now, "day");

                        // Filter out past time slots
                        const filteredTimeSlots = timeSlots.filter((time) => {
                          if (!isToday) return true;
                          const selectedDateTime = dayjs.tz(
                            `${appointmentDate} ${time}`,
                            "YYYY-MM-DD hh:mm A",
                            userTimezone
                          );
                          return selectedDateTime.isAfter(now);
                        });

                        const noSlotsAvailable = filteredTimeSlots.length === 0;

                        return (
                          <Select
                            value={formData.appointmentTime}
                            onValueChange={(value) =>
                              updateFormData("appointmentTime", value)
                            }
                            disabled={
                              noSlotsAvailable ||
                              formData?.appointmentDate === undefined
                            }
                          >
                            <SelectTrigger className="h-10 sm:h-12 border-gray-300 text-sm sm:text-base">
                              <SelectValue
                                placeholder={
                                  noSlotsAvailable
                                    ? "No slots available"
                                    : "Select time"
                                }
                              />
                            </SelectTrigger>
                            <SelectContent>
                              {noSlotsAvailable ? (
                                <SelectItem
                                  disabled
                                  value="none"
                                  className="text-sm sm:text-base"
                                >
                                  No slots available
                                </SelectItem>
                              ) : (
                                filteredTimeSlots.map((time) => (
                                  <SelectItem
                                    key={time}
                                    value={time}
                                    className="text-sm sm:text-base"
                                  >
                                    {time}
                                  </SelectItem>
                                ))
                              )}
                            </SelectContent>
                          </Select>
                        );
                      })()}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Mobile Submit Button */}
              <div className="lg:hidden">
                <div className="mt-10 flex justify-left w-full">
                  <ReCAPTCHA
                    sitekey="6LeiTIMrAAAAAIeeikGJzHNTVbZB-1W20iReEWtt" // Replace with your actual site key
                    // Secret Key for backend use
                    onChange={handleCaptchaChange}
                    theme="light" // or "dark"
                    size="normal" // or "compact"
                  />
                </div>

                {formErrors.captchaError && (
                  <span className="inline-flex mt-2 items-center rounded-md bg-red-50 px-2 py-1 text-xs font-medium text-red-700 ring-1 ring-red-600/10 ring-inset">
                    <span className="text-red-500 text-[12px]">
                      {formErrors.captchaError}
                    </span>
                  </span>
                )}

                <Button
                  type="button"
                  onClick={handleFormSubmission}
                  size="lg"
                  disabled={isSubmitting}
                  className="w-full mt-10 h-12 sm:h-14 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white font-semibold text-base sm:text-lg shadow-lg"
                >
                  {isSubmitting ? (
                    <div className="flex items-center gap-2">
                      <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      Booking Appointment...
                    </div>
                  ) : (
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-5 w-5" />
                      Book Appointment
                      {selectedAppointmentType?.id === "TELE_MEDICINE" && (
                        <span className="ml-2 font-bold">
                          ${selectedAppointmentType.price}
                        </span>
                      )}
                    </div>
                  )}
                </Button>
              </div>
            </div>

            {/* Desktop/Tablet Sidebar */}
            <div className="hidden lg:block lg:col-span-1">
              <div className="sticky top-20 space-y-4">
                <AppointmentSummary />

                {/* Contact Information */}
                {/* <Card className="shadow-lg border-0">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-lg">
                      <MapPin className="h-5 w-5 text-primary" />
                      Contact Us
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3 text-sm">
                    <div className="flex items-center gap-2">
                      <Phone className="h-4 w-4 text-gray-500" />
                      <span>(*************</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Mail className="h-4 w-4 text-gray-500" />
                      <span><EMAIL></span>
                    </div>
                    <div className="flex items-start gap-2">
                      <MapPin className="h-4 w-4 text-gray-500 mt-0.5" />
                      <span>
                        2765 Del Paso Rd 120,
                        <br />
                        Sacramento, CA 95835 US
                      </span>
                    </div>
                  </CardContent>
                </Card> */}

                <div className="mt-10 flex flex-col w-fit justify-left">
                  <ReCAPTCHA
                    sitekey="6LeiTIMrAAAAAIeeikGJzHNTVbZB-1W20iReEWtt" // Replace with your actual site key
                    // Secret Key for backend use
                    onChange={handleCaptchaChange}
                    theme="light" // or "dark"
                    size="normal" // or "compact"
                  />
                  {formErrors.captchaError && (
                    <span className="w-fit mt-2 inline-flex items-center rounded-md bg-red-50 px-2 py-1 text-xs font-medium text-red-700 ring-1 ring-red-600/10 ring-inset">
                      <span className="text-red-500 text-[12px]">
                        {formErrors.captchaError}
                      </span>
                    </span>
                  )}
                </div>

                {/* Desktop Submit Button */}
                <Button
                  type="button"
                  onClick={handleFormSubmission}
                  size="lg"
                  disabled={isSubmitting}
                  className="w-full h-14 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white font-semibold text-lg shadow-lg"
                >
                  {isSubmitting ? (
                    <div className="flex items-center gap-2">
                      <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      Booking Appointment...
                    </div>
                  ) : (
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-5 w-5" />
                      Book Appointment
                      {selectedAppointmentType?.id === "TELE_MEDICINE" && (
                        <span className="ml-2 font-bold">
                          ${selectedAppointmentType.price}
                        </span>
                      )}
                    </div>
                  )}
                </Button>
              </div>
            </div>
          </div>
        </form>
      </div>

      {showConfirmationModal && (
        <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50 ">
          <div className="bg-white rounded-2xl p-8 max-w-3xl w-full shadow-2xl text-center animate-slideIn border border-gray-200 mx-2">
            <div className="flex flex-col items-center space-y-4">
              <div className="bg-purple-100 text-blue-600 p-3 rounded-full">
                <svg
                  className="w-6 h-6"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth={2}
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M13 16h-1v-4h-1m1-4h.01M12 2a10 10 0 11-7.07 2.93A10 10 0 0112 2z"
                  />
                </svg>
              </div>

              <h3 className="text-2xl font-bold text-gray-800">Disclaimer</h3>

              {formData.apptType === "TELE_MEDICINE" ? (
                <p className="text-gray-600 text-base leading-relaxed">
                  This is a <strong>Telemedicine Appointment</strong>. A
                  physical examination is not possible in this setting. As a
                  result, not all conditions may be diagnosed or treated
                  effectively. If a hands-on evaluation is necessary, an
                  in-clinic visit may be recommended. <br />{" "}
                  <strong>
                    {" "}
                    You consent to receive SMS messages related to your
                    appointment.
                  </strong>
                </p>
              ) : (
                <p>
                  <strong>
                    By proceeding, you consent to receive SMS messages related
                    to your appointment.
                  </strong>
                </p>
              )}

              <div className="flex flex-col sm:flex-row justify-center gap-4 pt-6">
                <button
                  onClick={() => {
                    handleSubmit();
                    setShowConfirmationModal(false);
                  }}
                  className="bg-primary text-white px-6 py-2 cursor-pointer rounded-lg hover:primary transition"
                >
                  Agree
                </button>

                <button
                  onClick={() => {
                    setShowConfirmationModal(false);
                  }}
                  className="bg-red-600 text-white px-6 py-2 cursor-pointer rounded-lg hover:bg-red-700 transition"
                >
                  Go Back
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
