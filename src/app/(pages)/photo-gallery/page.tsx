"use client";

import HeroBanner from "@/app/components/hero-banner";
import { ChevronLeft, ChevronRight, X } from "lucide-react";
import Image from "next/image";
import React, { useCallback, useState } from "react";

type ImageType = {
  id: number;
  src: string;
  alt: string;
  title: string;
};

const images: ImageType[] = [
  {
    id: 1,
    src: "/images/photo-gallery-9.webp",
    alt: "Cute cat named Whiskers",
    title: "Whiskers",
  },
  {
    id: 2,
    src: "/images/photo-gallery-10.webp",
    alt: "Golden retriever named <PERSON>",
    title: "<PERSON>",
  },
  {
    id: 3,
    src: "/images/photo-gallery-11.webp",
    alt: "Tabby cat named <PERSON>",
    title: "<PERSON>",
  },
  {
    id: 4,
    src: "/images/photo-gallery-12.webp",
    alt: "Beagle dog named <PERSON>",
    title: "<PERSON>",
  },
  {
    id: 5,
    src: "/images/photo-gallery-13.webp",
    alt: "Orange cat named <PERSON>",
    title: "<PERSON>",
  },
  {
    id: 6,
    src: "/images/photo-gallery-14.webp",
    alt: "Husky dog named <PERSON>",
    title: "<PERSON>",
  },
];

export default function PhotoGallery() {
  const [selectedImage, setSelectedImage] = useState<ImageType | null>(null);
  const [currentIndex, setCurrentIndex] = useState(0);

  const openModal = (image: ImageType, index: number) => {
    setSelectedImage(image);
    setCurrentIndex(index);
  };

  const closeModal = () => {
    setSelectedImage(null);
  };

  const nextImage = useCallback(() => {
    const nextIndex = (currentIndex + 1) % images.length;
    setCurrentIndex(nextIndex);
    setSelectedImage(images[nextIndex]);
  }, [currentIndex]);

  const prevImage = useCallback(() => {
    const prevIndex = (currentIndex - 1 + images.length) % images.length;
    setCurrentIndex(prevIndex);
    setSelectedImage(images[prevIndex]);
  }, [currentIndex]);

  const handleKeyDown = useCallback(
    (e: KeyboardEvent) => {
      if (e.key === "Escape") closeModal();
      if (e.key === "ArrowRight") nextImage();
      if (e.key === "ArrowLeft") prevImage();
    },
    [nextImage, prevImage]
  );

  React.useEffect(() => {
    if (selectedImage) {
      document.addEventListener("keydown", handleKeyDown);
      document.body.style.overflow = "hidden";
      return () => {
        document.removeEventListener("keydown", handleKeyDown);
        document.body.style.overflow = "unset";
      };
    }
  }, [selectedImage, handleKeyDown]);

  return (
    <div>
      <HeroBanner
        heading={
          <>
            Photo <span className="text-blue-600">Gallery</span>
          </>
        }
        // description="If you would like to submit a photo, please click the button below."
        img="/images/photo-gallery.webp"
      />

      <section id="team" className="py-20 bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-1 gap-12 items-center text-center">
            <div className="space-y-6">
              <h2 className="text-2xl md:text-4xl font-bold text-black">
                We Love Our Patients
              </h2>
              <p className="text-gray-900 max-w-[45rem] m-auto">
                At Dry Creek Veterinary Hospital, our patients mean the world to
                us. They make what we do every day worth it. Check out some of
                the adorable faces of our wonderful patients.
              </p>
            </div>
          </div>
        </div>
      </section>

      <section id="services" className="py-20 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          {/* <div className="mx-auto mb-12">
            <h1 className="text-4xl font-bold text-center text-gray-900 mb-4">
              Photo Gallery
            </h1>
          </div> */}

          {/* Gallery Grid */}
          <div className="mx-auto max-w-7xl px-4">
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-6">
              {images.map((image, index) => (
                <div
                  key={image.id}
                  className="group cursor-pointer overflow-hidden rounded-xl shadow-md hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2"
                  onClick={() => openModal(image, index)}
                >
                  <div className="relative w-full h-44 sm:h-48 md:h-50 lg:h-60">
                    <Image
                      src={image.src}
                      alt={image.alt}
                      fill
                      className="object-cover object-center transition-transform duration-300 group-hover:scale-110"
                    />
                    {/* Overlay */}
                    <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition duration-300 flex items-center justify-center">
                      <div className="text-white text-center">
                        <h3 className="text-sm font-semibold mb-1">
                          {image.title}
                        </h3>
                        <p className="text-xs">Click to view</p>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Modal */}
          {selectedImage && (
            <div
              className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
              onClick={closeModal}
            >
              {/* Close button */}
              <button
                onClick={closeModal}
                className="absolute top-6 right-6 z-60 p-2 rounded-full bg-white/10 hover:bg-white/20 transition-colors duration-300"
                aria-label="Close modal"
              >
                <X size={28} className="text-white" />
              </button>

              {/* Navigation buttons */}
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  prevImage();
                }}
                className="absolute left-6 top-1/2 -translate-y-1/2 z-60 p-3 rounded-full bg-white/10 hover:bg-white/20 transition duration-300"
                aria-label="Previous image"
              >
                <ChevronLeft size={36} className="text-white" />
              </button>

              <button
                onClick={(e) => {
                  e.stopPropagation();
                  nextImage();
                }}
                className="absolute right-6 top-1/2 -translate-y-1/2 z-60 p-3 rounded-full bg-white/10 hover:bg-white/20 transition duration-300"
                aria-label="Next image"
              >
                <ChevronRight size={36} className="text-white" />
              </button>

              {/* Image container */}
              <div
                className="max-w-5xl w-full max-h-[85vh] flex flex-col items-center animate-fadeIn"
                onClick={(e) => e.stopPropagation()}
              >
                <Image
                  src={selectedImage.src}
                  alt={selectedImage.alt}
                  width={1200}
                  height={800}
                  className="max-h-[75vh] w-auto object-contain rounded-lg shadow-2xl transition-transform duration-500 ease-in-out"
                />

                {/* Caption */}
                <div className="w-full mt-4 px-4 py-3 bg-white/10 backdrop-blur-md rounded-lg text-center text-white">
                  <h2 className="text-xl font-semibold mb-1">
                    {selectedImage.title}
                  </h2>
                  <p className="text-sm text-gray-300">{selectedImage.alt}</p>
                  <p className="text-xs text-gray-400 mt-1">
                    {currentIndex + 1} of {images.length}
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      </section>
    </div>
  );
}
