"use client";
import CaptchaInput from "@/app/components/form/captcha-input";
import SignatureInput from "@/app/components/form/signature";
import TextInput from "@/app/components/form/text-input";
import HeroBanner from "@/app/components/hero-banner";
import {
  emailValidationRule,
  mobileValidationRule,
} from "@/app/constant/constant";
import { useNotificationContext } from "@/app/context/NotificationContext";
import { usePostApi } from "@/app/services/useApi";
import Link from "next/link";
import { useRef } from "react";
import { useForm } from "react-hook-form";

interface NewClientFormTypes {
  firstName: string;
  lastName: string;
  phone: string;
  email: string;
  signature: string;
  date: string;
  petName: string;
  captcha: string;
}

const ConsentForm = () => {
  const newClientFormRef = useRef<HTMLDivElement>(null);
  const { notify } = useNotificationContext();
  const { postData: postContactSendData } =
      usePostApi("");

  const { control } = useForm<NewClientFormTypes>({
    defaultValues: { email: "" },
  });

  const handleScrollToForm = () => {
    if (newClientFormRef.current) {
      const navbarHeight = 100;
      const elementPosition =
        newClientFormRef.current.getBoundingClientRect().top +
        window.pageYOffset;
      const offsetPosition = elementPosition - navbarHeight;

      window.scrollTo({
        top: offsetPosition,
        behavior: "smooth",
      });
    }
  };

  // const onSubmit = async (data: NewClientFormTypes) => {
  //   const response = await fetch("http://localhost:8080/python/mail.py", {
  //     method: "POST",
  //     headers: { "Content-Type": "application/json" },
  //     body: JSON.stringify(data),
  //   });

  //   const text = await response.text(); // 👈 get raw response
  //   console.log("Raw response:", text);

  //   let result;
  //   try {
  //     result = JSON.parse(text);
  //   } catch {
  //     result = { success: false, error: "Invalid JSON returned", raw: text };
  //   }
  //   console.log("Parsed result:", result);
  // };

  const handleSubmit = async () => {
    // if (!validateForm()) return;

    try {
      const response = await postContactSendData(
        FormData,
        "https://sabozq2hir5m6cnabqshvc7ma40lnoxq.lambda-url.us-west-2.on.aws/consentForm"
      );

      if (response?.status === 200) {
        notify("Message sent successfully!", "success");
        // setFormData({
        //   firstName: "",
        //   lastName: "",
        //   email: "",
        //   phoneNumber: "",
        //   message: "",
        // });
        // setErrors({});
      } else {
        notify("Failed to send message. Try again later.", "error");
        console.error("Failed to send message. Try again later.");
      }
    } catch (error) {
      console.error("Error sending message:", error);
    }
  };

  return (
    <div>
      <HeroBanner
        heading={
          <>
            Consent <span className="text-blue-600">Form</span>
          </>
        }
        description="   Please fill out this form as completely and accurately as
                possible so we can get to know you and your pet(s) before your
                visit."
        img="/images/consent-form-pets-image.webp"
        buttonLabel="Get Started"
        onClick={handleScrollToForm}
      />

      <section
        ref={newClientFormRef}
        className="mt-12 border border-gray-300 p-4 my-8 rounded-md scroll-smooth py-10 max-w-6xl mx-auto grid grid-cols-1 md:grid-cols-2 gap-6"
      >
        {/* Name */}
        <TextInput
          control={control}
          name="firstName"
          label="First Name"
          placeholder="First Name"
          required
        />
        <TextInput
          control={control}
          name="lastName"
          label="Last Name"
          placeholder="Last Name"
          required
        />
        <TextInput
          control={control}
          name="phone"
          label="Phone"
          placeholder="Phone"
          type="tel"
          maxLength={10}
          required
          rules={mobileValidationRule}
        />

        <TextInput
          control={control}
          name="email"
          label="Email"
          placeholder="Email"
          type="email"
          required
          rules={emailValidationRule}
        />

        <TextInput
          control={control}
          name="petName"
          label="Pet's Name"
          placeholder="Pet's Name"
          required
        />
        <div className="col-span-2">
          <p className=" text-black mb-6">Dear Client,</p>

          <p className=" text-black mb-6">
            If you have any questions, please feel free to call or email the
            hospital at{" "}
            <Link
              href="tel:+12097459130"
              className="text-blue-800 font-semibold"
            >
              (*************{" "}
            </Link>{" "}
            /{" "}
            <Link
              href={`mailto:<EMAIL>`}
              className="text-blue-800 font-semibold"
            >
              <EMAIL>
            </Link>{" "}
            . On your pets surgery day, you will be required to review and sign
            an Authorization/Estimate Form. We will also double-check to ensure
            we have a phone number where you can be reached during your pets
            surgery time.
          </p>

          <p className=" text-black mb-6">
            The night before your pet’s surgery, we ask that you refrain from
            providing any food or treats after 10:00 pm. Water is fine and may
            be left out for your pet to consume. If you are currently
            administering any medications, vitamins, and/or injections, withhold
            the morning doses unless otherwise instructed by the vet.
          </p>

          <p className=" text-black mb-6">
            At the time of your pets scheduled surgery, the vet will address any
            remaining questions or concerns you may have and will collect the
            Authorization Form that you have completed.
          </p>

          <p className=" text-black mb-6">
            If you have elected to have any of the recommended blood tests
            performed, these will be collected and done prior to the start of
            surgery, if not already performed in advance. If any questions
            arise, the vet may contact you at the number on the Authorization
            Form.
          </p>

          <p className=" text-black mb-6">
            Upon the completion of the surgery, the vet will process your
            payment, go over all discharge orders verbally, and provide you with
            a written copy. If you do not understand any instructions, please do
            not hesitate to ask for clarification prior to or after our
            departure.
          </p>

          <p className=" text-black mb-6">
            We know surgery days can induce stress and anxiety.
            <Link
              href="https://drycreekvet.com/about-dry-creek-veterinary-hospital/"
              target="_blank"
              className="text-blue-800 font-semibold"
            >
              {" "}
              Dry Creek Veterinary Hospital
            </Link>{" "}
            appreciates the trust you place in us, and we want to make the
            experience as pleasant as possible for both you and your pet.
          </p>

          <p className=" text-black">
            We look forward to serving you and your pet on the upcoming surgery
            day.
          </p>
        </div>

        <div className="col-span-2">
          <SignatureInput
            control={control}
            name="signature"
            label="Signature"
            required
          />
        </div>
        <TextInput
          control={control}
          name="date"
          label="Date"
          type="date"
          required
        />
        {/* ✅ Captcha here */}
        <CaptchaInput
          control={control}
          name="captcha"
          label="Verify you are human"
          className="col-span-2 mt-3"
        />
        <div className="col-span-2 mt-3">
          <button
            onClick={handleSubmit}
            className=" bg-blue-600 cursor-pointer text-white px-6 py-2 rounded-full text-lg font-semibold hover:bg-blue-700 transition-colors flex items-center justify-center"
          >
            Submit
          </button>
        </div>
      </section>
    </div>
  );
};
export default ConsentForm;
