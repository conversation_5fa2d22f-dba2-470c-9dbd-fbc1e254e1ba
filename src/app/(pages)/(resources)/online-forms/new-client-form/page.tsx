"use client";
import CaptchaInput from "@/app/components/form/captcha-input";
import CheckboxInput from "@/app/components/form/check-box-input";
import RadioInput from "@/app/components/form/radio-input";
import SelectInput from "@/app/components/form/select";
import TextInput from "@/app/components/form/text-input";
import HeroBanner from "@/app/components/hero-banner";
import {
  emailValidationRule,
  mobileValidationRule,
  zipCodeValidationRule,
} from "@/app/constant/constant";
import { useRef } from "react";
import { useFieldArray, useForm } from "react-hook-form";

interface PetListType {
  name: string;
  species: string;
  breed: string;
  dob: string;
  sex: string;
  microchipIdentification: string;
  microchipNumber: string;
  hasNext: number;
}
interface NewClientFormTypes {
  firstName: string;
  lastName: string;
  email: string;
  primaryPhone: string;
  secondaryPhone: string;
  streetAddress: string;
  addressLine2?: string;
  city: string;
  state: string;
  zipCode: string;
  captcha: string;
  // authorized
  authFirstName: string;
  authLastName: string;
  authPhone: string;
  referralSource: string;
  pets: PetListType[];
  agreeTerms: boolean;
}

const NewClientForm = () => {
  const newClientFormRef = useRef<HTMLDivElement>(null);

  const { control, handleSubmit, watch } = useForm<NewClientFormTypes>({
    defaultValues: {
      pets: [
        {
          name: "",
          species: "",
          breed: "",
          dob: "",
          sex: "",
          microchipIdentification: "",
          microchipNumber: "",
        },
      ],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: "pets",
  });

  const MAX_PETS = 5;

  const petsWatch = watch("pets");

  const handleScrollToForm = () => {
    if (newClientFormRef.current) {
      const navbarHeight = 100; // Adjust this to your navbar height in px
      const elementPosition =
        newClientFormRef.current.getBoundingClientRect().top +
        window.pageYOffset;
      const offsetPosition = elementPosition - navbarHeight;

      window.scrollTo({
        top: offsetPosition,
        behavior: "smooth",
      });
    }
  };

  // For SelectInput
  const usStatesOptions = [
    { label: "Alabama", value: "AL" },
    { label: "Alaska", value: "AK" },
    { label: "Arizona", value: "AZ" },
    { label: "Arkansas", value: "AR" },
    { label: "California", value: "CA" },
    { label: "Colorado", value: "CO" },
    { label: "Connecticut", value: "CT" },
    { label: "Delaware", value: "DE" },
    { label: "Florida", value: "FL" },
    { label: "Georgia", value: "GA" },
    { label: "Hawaii", value: "HI" },
    { label: "Idaho", value: "ID" },
    { label: "Illinois", value: "IL" },
    { label: "Indiana", value: "IN" },
    { label: "Iowa", value: "IA" },
    { label: "Kansas", value: "KS" },
    { label: "Kentucky", value: "KY" },
    { label: "Louisiana", value: "LA" },
    { label: "Maine", value: "ME" },
    { label: "Maryland", value: "MD" },
    { label: "Massachusetts", value: "MA" },
    { label: "Michigan", value: "MI" },
    { label: "Minnesota", value: "MN" },
    { label: "Mississippi", value: "MS" },
    { label: "Missouri", value: "MO" },
    { label: "Montana", value: "MT" },
    { label: "Nebraska", value: "NE" },
    { label: "Nevada", value: "NV" },
    { label: "New Hampshire", value: "NH" },
    { label: "New Jersey", value: "NJ" },
    { label: "New Mexico", value: "NM" },
    { label: "New York", value: "NY" },
    { label: "North Carolina", value: "NC" },
    { label: "North Dakota", value: "ND" },
    { label: "Ohio", value: "OH" },
    { label: "Oklahoma", value: "OK" },
    { label: "Oregon", value: "OR" },
    { label: "Pennsylvania", value: "PA" },
    { label: "Rhode Island", value: "RI" },
    { label: "South Carolina", value: "SC" },
    { label: "South Dakota", value: "SD" },
    { label: "Tennessee", value: "TN" },
    { label: "Texas", value: "TX" },
    { label: "Utah", value: "UT" },
    { label: "Vermont", value: "VT" },
    { label: "Virginia", value: "VA" },
    { label: "Washington", value: "WA" },
    { label: "West Virginia", value: "WV" },
    { label: "Wisconsin", value: "WI" },
    { label: "Wyoming", value: "WY" },
  ];

  // For RadioInput
  const sexOptions = [
    { label: "Male", value: "male" },
    { label: "Neutered Male", value: "neutered_male" },
    { label: "Female", value: "female" },
    { label: "Spayed Female", value: "spayed_female" },
  ];

  const yesNoOptions = [
    { label: "Yes", value: "yes" },
    { label: "No", value: "no" },
  ];

  const getOrdinalLabel = (n: number) => {
    const ordinals = ["first", "second", "third", "fourth", "fifth"];
    return ordinals[n] || `${n + 1}th`;
  };

  const onSubmit = (data: NewClientFormTypes) => {
    console.log(data);
  };

  return (
    <div>
      <HeroBanner
        heading={
          <>
            New Client
            <span className="text-blue-600">Form</span>
          </>
        }
        description=" Please fill out this form as completely and accurately as
                possible so we can get to know you and your pet(s) before your
                visit."
        img="/images/new-client-form-pets-image.webp"
        buttonLabel="Get Started"
        onClick={handleScrollToForm}
      />

      <section
        ref={newClientFormRef}
        className="mt-12 border border-gray-300 p-4 my-8 rounded-md scroll-smooth py-10 max-w-6xl mx-auto grid grid-cols-1 md:grid-cols-2 gap-6"
      >
        {/* Name */}
        <TextInput
          control={control}
          name="firstName"
          label="First Name"
          placeholder="First Name"
          required
        />
        <TextInput
          control={control}
          name="lastName"
          label="Last Name"
          placeholder="Last Name"
          required
        />
        {/* Email and Phone */}
        <TextInput
          control={control}
          name="email"
          label="Email"
          placeholder="Email"
          type="email"
          required
          rules={emailValidationRule}
        />
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <TextInput
            control={control}
            name="primaryPhone"
            label="Primary Phone"
            placeholder="Primary Phone"
            type="tel"
            maxLength={10}
            required
            rules={mobileValidationRule}
          />

          <TextInput
            control={control}
            name="secondaryPhone"
            label="Secondary Phone"
            placeholder="Secondary Phone"
            type="tel"
            maxLength={10}
            rules={mobileValidationRule}
          />
        </div>
        {/* Address */}
        <TextInput
          control={control}
          name="streetAddress"
          label="Address"
          placeholder="Street Address"
          required
        />
        <TextInput
          control={control}
          name="addressLine2"
          label="Address Line 2"
          placeholder="Apartment, suite, etc."
          required
        />
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* City, State, Zip */}
          <TextInput
            control={control}
            name="city"
            label="City"
            placeholder="City"
            required
          />
          <TextInput
            control={control}
            name="zipCode"
            label="Zip Code"
            placeholder="12345 / 12345-6789"
            required
            rules={zipCodeValidationRule}
          />
        </div>
        <SelectInput
          control={control}
          name="state"
          label="State"
          placeholder="Select state"
          options={usStatesOptions}
          required
          // className="col-span-2"
        />
        {/* Authorized Decision Maker */}
        <p className="pt-3 font-medium text-xl col-span-2">
          Who else is authorized to make decisions about your pets healthcare?
        </p>
        <TextInput
          control={control}
          name="authFirstName"
          label="First Name"
          placeholder="First Name"
          required
        />
        <TextInput
          control={control}
          name="authLastName"
          label="Last Name"
          placeholder="Last Name"
          required
        />
        <TextInput
          control={control}
          name="authPhone"
          label="Phone"
          placeholder="Phone"
          type="tel"
        />
        {/* Referral */}
        <TextInput
          control={control}
          name="referralSource"
          label="How did you find out about our hospital? If you were referred by someone, who should we thank?"
          placeholder="Google, friend, flyer, etc."
          className="col-span-2"
          required
        />
        {/* Practice Info */}
        {fields.map((field, index) => {
          const microchipField = petsWatch?.[index]?.microchipIdentification;
          return (
            <div
              key={field.id}
              className="col-span-2 grid grid-cols-1 md:grid-cols-2 gap-4 border p-3 rounded-md border-gray-300"
            >
              <TextInput
                control={control}
                name={`pets.${index}.name`}
                label="Pet's Name"
                placeholder="e.g. Bella"
                required
              />

              <TextInput
                control={control}
                name={`pets.${index}.species`}
                label="Species (Dog, cat, etc.)"
                placeholder="e.g. Dog, Cat"
                required
              />

              <TextInput
                control={control}
                name={`pets.${index}.breed`}
                label="Breed"
                placeholder="e.g. Labrador"
                required
              />

              <TextInput
                control={control}
                name={`pets.${index}.dob`}
                label="Date of Birth"
                type="date"
                required
              />

              <RadioInput
                control={control}
                name={`pets.${index}.sex`}
                label="Sex"
                options={sexOptions}
                required
              />

              {/* Add another pet option (only show if less than 5) */}
              {index < MAX_PETS - 1 && (
                <RadioInput
                  control={control}
                  name={`pets.${index}.hasNext`}
                  label={`Do you have a ${getOrdinalLabel(index + 1)} pet?`}
                  options={yesNoOptions}
                  required
                  onChange={(value: string) => {
                    if (value === "yes" && fields.length < MAX_PETS) {
                      append({
                        name: "",
                        species: "",
                        breed: "",
                        dob: "",
                        sex: "",
                        microchipIdentification: "",
                        microchipNumber: "",
                        hasNext: index,
                      });
                    } else if (value === "no") {
                      remove(index + 1);
                    }
                  }}
                />
              )}

              <RadioInput
                control={control}
                name={`pets.${index}.microchipIdentification`}
                label="Does your pet have a microchip identification?"
                options={yesNoOptions}
                required
              />

              {/* Conditional microchip number field */}
              {microchipField === "yes" && (
                <TextInput
                  control={control}
                  name={`pets.${index}.microchipNumber`}
                  label="Microchip Number"
                  placeholder="e.g. 985141002389456"
                  required
                />
              )}
            </div>
          );
        })}

        <div className="col-span-2">
          Payment is due in full at the time that services are performed. If
          being admitted into the hospital, we cannot begin the care of your pet
          until you have confirmed your desire to do so by 1) signing the client
          consent & estimate form, and 2) leaving an initial deposit of 50% of
          the upper end of the estimate. This is the only way that we have of
          knowing for certain that you want us to proceed with the care of your
          pet. We accept all major credit cards, cash, and CareCredit. All open
          invoices are sent to collections after 45 days unless prior
          arrangements are made. *
        </div>
        {/* Agreement */}
        <CheckboxInput
          control={control}
          name="agreeTerms"
          label="I agree to the terms and conditions"
          required
          className="col-span-2"
        />
        <CaptchaInput
          control={control}
          name="captcha"
          label="Verify you are human"
          className="col-span-2 mt-3"
        />
        <div>
          <button
            onClick={handleSubmit(onSubmit)}
            className="col-span-2 bg-blue-600 cursor-pointer text-white px-6 py-2 rounded-full text-lg font-semibold hover:bg-blue-700 transition-colors flex items-center justify-center"
          >
            Submit
          </button>
        </div>
      </section>
    </div>
  );
};
export default NewClientForm;
