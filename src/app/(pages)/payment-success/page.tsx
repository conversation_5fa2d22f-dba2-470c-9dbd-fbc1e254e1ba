"use client";
import successAnimation from "@/app/animations/success.json";
import <PERSON><PERSON> from "lottie-react";
import React, { useEffect } from "react";

const PaymentSuccess: React.FC = () => {
  //close tab on successful payment after 5 seconds

  useEffect(() => {
    setTimeout(() => {
      window.close();
    }, 5000);
  }, []);

  return (
    <div className="min-h-screen flex flex-col items-center justify-center text-center px-4">
      <div className="w-72 h-72">
        <Lottie animationData={successAnimation} loop={false} autoplay={true} />
      </div>
      <h1 className="text-2xl md:text-4xl font-bold text-green-700 mb-8">
        Payment Successful
      </h1>
      <p className="text-md text-gray-700 hidden md:block">
        Thank you! Your payment has been received and your appointment is
        confirmed.
      </p>
      <p className="text-md mb-6 text-gray-700 hidden md:block">
        You will receive the booking details on your registered phone number
        shortly.
      </p>
      <p className="text-md text-gray-700 md:hidden mb-6">
        Your appointment is confirmed. <br />
        Details will be sent to your phone.
      </p>
    </div>
  );
};

export default PaymentSuccess;
