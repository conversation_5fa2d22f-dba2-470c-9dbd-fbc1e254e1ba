"use client";

import HeroBanner from "@/app/components/hero-banner";
import { ArrowRight, ChevronDownIcon } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useState } from "react";

const VeterinaryFAQServices = () => {
  const [openFAQ, setOpenFAQ] = useState<number | null>(null);

  const faqList = [
    {
      label: "What types of payment options are available?",
      ans: "Payment in full is expected at the time services are provided or upon discharge of your pet. We accept payment by cash, check, Visa, MasterCard, Discover, and American Express.",
    },
    {
      label: "Are there payment plans available for my pet’s care?",
      ans: "We offer CareCredit as a convenient financing option for pet medical bills. They offer an easy application process that will give you results in minutes.",
    },
    {
      label: "Will I be provided with an estimate of costs?",
      ans: (
        <div className="space-y-2">
          <p>
            We will provide a good-faith estimate of the cost of our services
            before treating your pet. However, unforeseen circumstances may
            arise that impact your final bill.
          </p>
          <p>
            We will always discuss our written estimate with all clients before
            they leave our facility, and require a deposit based on our estimate
            before any further treatment.
          </p>
        </div>
      ),
    },
    {
      label: "Should I call ahead to book an appointment?",
      ans: (
        <div className="space-y-2">
          <p>
            Yes, please call ahead to book an appointment to ensure we have room
            in our schedule to see your pet.
          </p>
          <p>
            If your pet needs emergency care during our regular business hours,
            please contact us right away.
          </p>
          <Link
            href="/contact-us"
            className="flex items-center gap-3 text-blue-600 pt-2 hover:text-blue-500"
          >
            Book Your Next Appointment <ArrowRight className="h-5 w-5" />{" "}
          </Link>
        </div>
      ),
    },
    {
      label: "What is your appointment cancellation policy?",
      ans: "If you need to cancel or reschedule an appointment, please let us know as soon as possible so that we can offer the space in our schedule to other clients.",
    },
    {
      label: "What types of animals do you treat?",
      ans: "Our team is equipped to take care of the needs of cats and dogs in Sacramento, and its surrounding areas.",
    },
    {
      label: "Where can I purchase my pet's prescription and food?",
      ans: "You can purchase all the medications and specialty food your pet needs from our in-house pharmacy.",
    },
    {
      label: "What are the fees associated with a specialty appointment?",
      ans: (
        <div className="space-y-2">
          <p>
            You are financially responsible for the initial examination fee and
            any diagnostics or treatment that you consent to for your pet’s
            treatment. This payment is due at the time of service.
          </p>
          <p>
            For further hospitalization, diagnostics, or surgery, a deposit is
            required upfront with payment due in full when your pet is
            discharged.
          </p>
        </div>
      ),
    },
    {
      label: "Can you help with my pet insurance?",
      ans: "We’re happy to help you fill out pet insurance forms for your pet’s visit.",
    },
  ];

  const services = [
    {
      icon: "🏥",
      title: "Comprehensive Care",
      color: "bg-blue-50",
    },
    {
      icon: "🛡️",
      title: "State-of-the-Art Technology",
      color: "bg-blue-50",
    },
    {
      icon: "❤️",
      title: "Compassionate Staff",
      color: "bg-blue-50",
    },
    {
      icon: "🎯",
      title: "Preventative Health Focus",
      color: "bg-blue-50",
    },
    {
      icon: "🏆",
      title: "Accredited Excellence",
      color: "bg-blue-50",
    },
  ];

  const toggleFAQ = (index: number) => {
    setOpenFAQ(openFAQ === index ? null : index);
  };

  return (
    <>
      <HeroBanner
        heading={<span className="text-blue-600 block">FAQs</span>}
        description="There is no question too big or too small for our veterinary team. Below are some answers to our most common questions."
        img="/images/faq.jpg"
      />

      <div className="min-h-screen bg-gray-50">
        <div className="py-16 px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-2xl md:text-4xl font-bold text-center mb-12 text-gray-900 ">
              Frequently Asked Questions
            </h2>

            <div className="space-y-4">
              {faqList?.map((question, index) => (
                <div
                  key={`${index}-${question?.label}`}
                  className="bg-white rounded-lg shadow-sm border border-gray-200"
                >
                  <button
                    onClick={() => toggleFAQ(index)}
                    className="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-inset rounded-lg"
                  >
                    <span className="text-blue-500 font-medium text-lg">
                      {question?.label}
                    </span>
                    <ChevronDownIcon
                      className={`w-5 h-5 text-blue-600 transform transition-transform duration-200 ${
                        openFAQ === index ? "rotate-180" : ""
                      }`}
                    />
                  </button>

                  {openFAQ === index && (
                    <div className="px-6 pb-4">
                      <div className="border-t pt-4 text-gray-600">
                        {question?.ans}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Veterinary Medical Center Difference Section */}
        <div className="py-16 px-4 sm:px-6 lg:px-8 bg-blue-50">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-2xl md:text-4xl font-bold text-center mb-12 text-blue-600 ">
              Veterinary Medical Center Difference
            </h2>

            <div className="flex flex-col lg:flex-row items-center gap-12">
              {/* Pet Image */}
              <div className="lg:w-1/4 flex justify-center">
                <div className="w-40 h-40 rounded-full overflow-hidden shadow-lg bg-gray-800">
                  <Image
                    src="/images/dog-cat-with-veterinarian-faqs.avif"
                    alt="Dog and Cat"
                    width={160}
                    height={160}
                    className="w-full h-full object-cover"
                  />
                </div>
              </div>

              {/* Services Grid */}
              <div className="lg:w-3/4">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
                  {services.map((service, index) => (
                    <div
                      key={index}
                      className={`${service.color} bg-gradient-to-r from-gray-100 via-white to-gray-200 cursor-pointer rounded-lg p-6 text-center bg- shadow-sm hover:shadow-md transition-shadow duration-300`}
                    >
                      <div className="text-3xl mb-3">{service.icon}</div>
                      <h3 className="text-blue-600 font-semibold text-sm leading-tight">
                        {service.title}
                      </h3>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default VeterinaryFAQServices;
