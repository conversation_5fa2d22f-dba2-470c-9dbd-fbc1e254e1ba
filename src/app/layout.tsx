import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_<PERSON><PERSON> } from "next/font/google";
import Head from "next/head";
import Footer from "./components/footer";
import Header from "./components/header";
import { NotificationProvider } from "./context/NotificationContext";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Best Veterinary Hospital In Galt CA | Dry Creek Vet Hospital",
  description:
    "At Dry Creek Veterinary Hospital in Galt, CA, we offer personalized veterinary care ensuring your pets receive the best possible treatment. Emergency services, routine checkups, and specialized pet care.",
  keywords:
    "veterinary hospital, pet care, animal hospital, Galt CA, emergency vet, pet surgery, veterinary services",
  authors: [{ name: "Dry Creek Veterinary Hospital" }],
  creator: "Dry Creek Veterinary Hospital",
  publisher: "Dry Creek Veterinary Hospital",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL("https://drycreekvet.com"),
  alternates: {
    canonical: "/",
  },
  openGraph: {
    title: "Dry Creek Veterinary Hospital - Best Vet in Galt, CA",
    description:
      "Professional veterinary care for your pets in Galt, California. Emergency services, surgeries, and routine checkups.",
    url: "https://drycreekvet.com",
    siteName: "Dry Creek Veterinary Hospital",
    locale: "en_US",
    type: "website",
    images: [
      {
        url: "/images/friendly-veterinarian-with-pet-animal-vector.webp",
        width: 1200,
        height: 630,
        alt: "Dry Creek Veterinary Hospital - Caring for Pets in Galt, CA",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Dry Creek Veterinary Hospital - Best Vet in Galt, CA",
    description:
      "Professional veterinary care for your pets in Galt, California",
    images: ["/images/friendly-veterinarian-with-pet-animal-vector.webp"],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  verification: {
    google: "your-google-verification-code", // Replace with actual verification code
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const jsonLd = {
    "@context": "https://schema.org",
    "@type": "VeterinaryCare",
    name: "Dry Creek Veterinary Hospital",
    description:
      "Best Veterinary Hospital in Galt, CA offering personalized veterinary care for pets",
    url: "https://drycreekvet.com",
    telephone: "******-745-9130",
    address: {
      "@type": "PostalAddress",
      streetAddress: "1000 C Street",
      addressLocality: "#110 Galt",
      addressRegion: "CA",
      postalCode: "95632",
      addressCountry: "US",
    },
    openingHours: ["Mo-Fr 08:00-19:00", "Sa 09:00-17:00"],
    priceRange: "$$",
    medicalSpecialty: "Veterinary",
    serviceArea: {
      "@type": "GeoCircle",
      geoMidpoint: {
        "@type": "GeoCoordinates",
        latitude: 38.2546,
        longitude: -121.2999,
      },
      geoRadius: "16093", // 10 miles in meters
    },
    sameAs: [
      "https://www.instagram.com/drycreekvethospital/",
      "https://www.facebook.com/DryCreekVetHospital/",
      "https://www.yelp.com/biz/dry-creek-veterinary-hospital-galt",
    ],
  };

  return (
    <html lang="en">
      <Head>
        <link rel="preload" href="/images/light-wool.png" as="image" />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
        />
      </Head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <Header />

        <div className="pt-20">
          <div className="flex items-center justify-center gap-2 text-center px-4 py-3 bg-blue-50 text-blue-900 font-medium shadow-sm md:mt-0 mt-[22px] border-y border-blue-200">
            <span>
              Due to limited staffing, we are now accepting emergencies only on
              a priority basis on{" "}
              <span className="font-semibold">Saturdays</span>.
            </span>
          </div>
          <NotificationProvider>{children}</NotificationProvider>
        </div>
        <Footer />
      </body>
    </html>
  );
}
