/* eslint-disable @typescript-eslint/no-explicit-any */
import { useCallback, useEffect, useState } from "react";
import AxiosClient from "./interceptor";
import { ENV_VARIABLES } from "./config";

interface Options<T = any> {
  params?: T;
  isToaster?: boolean;
  fetch?: boolean;
  payload?: T;
  skipToasterForStatusCodes?: number[];
}

// generic service / GET API CALL
export const useGetApi = (path: string, options: Options = {}) => {
  const { params, isToaster = false, fetch, payload } = options;
  const [data, setData] = useState<any | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null | any>(null);

  const getData = useCallback(
    async (newPath?: string) => {
      setIsLoading(true);
      setError(null);

      try {
        const response = await AxiosClient.get(newPath || path, params || {});
        if (response && response?.data) {
          setData(response?.data);
          if (response?.data?.message && isToaster) {
            // toast({
            //   title: response?.data?.message,
            //   variant: "success",
            //   duration: 3000,
            // });
          }
          return response;
        }
      } catch (error: any) {
        errorLog(error, setError, isToaster);
        return error;
      } finally {
        setIsLoading(false);
      }
    },
    [path, params, isToaster]
  );

  useEffect(() => {
    if (fetch) {
      getData(payload);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fetch, getData]);

  return { data, isLoading, error, getData };
};

// generic service / POST API call
export const usePostApi = (path: string, options: Options = {}) => {
  const { params, isToaster = true, fetch, payload } = options;
  const [data, setData] = useState<any | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [errorData, setErrorData] = useState<any | null>(null);
  const [progress, setProgress] = useState<number>(0);

  const postData = useCallback(
    async (body?: any, newPath?: string, token?: string) => {
      setIsLoading(true);
      setError(null);

      try {
        const response = await AxiosClient.post(newPath || path, body, {
          ...(params || {}),
          onUploadProgress: (progressEvent: any) => {
            const progressValue =
              (progressEvent.loaded / progressEvent.total) * 100;
            setProgress(progressValue);
          },
          headers: {
            Authorization: token ? `Bearer ${token}` : undefined,
          },
        });
        if (response && response?.data) {
          setData(response?.data);
          setProgress(0); // Reset progress
          if (response?.data?.message && isToaster) {
            // toast({
            //   title: response?.data?.message,
            //   variant: "success",
            //   duration: 3000,
            // });
          }
          return response;
        }
      } catch (error: any) {
        setErrorData(error?.response?.data);
        errorLog(error, setError, isToaster);
        setProgress(0); // Reset progress
        return error;
      } finally {
        setIsLoading(false);
      }
    },
    [path, params, isToaster]
  );

  useEffect(() => {
    if (fetch) {
      postData(payload);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fetch, postData]);

  return { data, isLoading, error, postData, errorData, progress };
};

// generic service / PUT API call
export const usePutApi = (path: string, options: Options = {}) => {
  const { params, isToaster = true } = options;

  const [data, setData] = useState<any | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const putData = useCallback(
    async (body?: any) => {
      setIsLoading(true);
      setError(null);

      try {
        const response = await AxiosClient.put(path, body, params || {});
        if (response && response?.data) {
          setData(response?.data);

          if (response?.data?.message && isToaster) {
            // toast({
            //   title: response?.data?.message,
            //   variant: "success",
            //   duration: 3000,
            // });
          }
          return response;
        }
      } catch (error: any) {
        errorLog(error, setError, isToaster);
        return { error: error?.response?.data?.message || "Something went wrong" };
      } finally {
        setIsLoading(false);
      }
    },
    [path, params, isToaster]
  );

  return { data, isLoading, error, putData };
};

// generic service / DELETE API call
export const useDeleteApi = () => {
  const [data, setData] = useState<any | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const deleteData = useCallback(
    async (path: string, body?: any, isToaster: boolean = true) => {
      setIsLoading(true);
      setError(null);

      try {
        const response = await AxiosClient.delete(path, { data: body });
        if (response && response?.data) {
          setData(response.data);
          if (response?.data?.message && isToaster) {
            // toast({
            //   title: response?.data?.message,
            //   variant: "success",
            //   duration: 3000,
            // });
          }
          return response;
        }
      } catch (error: any) {
        errorLog(error, setError, isToaster);
      } finally {
        setIsLoading(false);
      }
    },
    []
  );

  return { data, isLoading, error, deleteData };
};

// generic service / PATCH API call
export const usePatchApi = () => {
  const [data, setData] = useState<any | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const patchData = useCallback(
    async (path: string, isToaster: boolean = true) => {
      setIsLoading(true);
      setError(null);

      try {
        const response = await AxiosClient.patch(path);
        if (response && response?.data) {
          setData(response.data);
          if (response?.data?.message && isToaster) {
            // toast({
            //   title: response?.data?.message,
            //   variant: "success",
            //   duration: 3000,
            // });
          }
          return response;
        }
      } catch (error: any) {
        errorLog(error, setError, isToaster);
      } finally {
        setIsLoading(false);
      }
    },
    []
  );

  return { data, isLoading, error, patchData };
};

export const useGetFileBlob = (path: string, options: Options = {}) => {
  const { isToaster = true } = options;
  const [data, setData] = useState<any | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const getFileBlob = useCallback(
    async (newPath?: string) => {
      setIsLoading(true);
      setError(null);

      try {
        const authorizationToken = localStorage.getItem("token");

        const response = await fetch(
          `${ENV_VARIABLES?.API_BASE}${newPath ?? path}`,
          {
            method: "GET",
            headers: {
              Authorization: `Bearer ${authorizationToken}`,
            },
          }
        );

        if (!response?.ok) {
          throw new Error(`Failed to fetch file: ${response.statusText}`);
        }

        const blob = await response.blob();

        const documentData = {
          fileBlobUrl: URL.createObjectURL(blob),
          fileName: response.headers.get("file-name") || "downloaded-file",
          mimeType: response.headers.get("Content-Type"),
        };
        setData(documentData);

        return blob; // Return document data for further use
      } catch (error: any) {
        errorLog(error, setError, isToaster);
      } finally {
        setIsLoading(false);
      }
    },
    [isToaster, path]
  );

  return { data, isLoading, error, getFileBlob };
};

// generic error log
const errorLog = (
  error: any,
  setError: React.Dispatch<React.SetStateAction<string | null>>,
  isToaster?: boolean
) => {
  if (error && error?.response?.data) {    
    setError(error?.response?.data?.message);
    if (isToaster) {
      //   toast({
      //     title: error?.response?.data?.message,
      //     variant: "destructive",
      //     duration: 3000,
      //   });
    }
  } else {
    setError("Unexpected error occurred... Please try again!");
  }
};
