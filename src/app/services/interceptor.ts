import { PublicAPIs } from "@/app/constant/public-apis";
import axios, { AxiosInstance } from "axios";
import { ENV_VARIABLES } from "./config";

const AxiosClient: AxiosInstance = axios.create({
  baseURL: ENV_VARIABLES.API_BASE,
});

// Add request interceptor
AxiosClient.interceptors.request.use(
  (config) => {
    const authorizationToken = localStorage.getItem("token");
    // Check if the request URL is a public API
    const isPublicAPI = PublicAPIs.some((api) => config?.url === api);

    if (authorizationToken && !isPublicAPI) {
      config.headers!["Authorization"] = `Bearer ${authorizationToken}`;
    }
    config.headers["clinic-id"] = "606b5bbf-abe2-4095-a12b-609b8c0d52e8";

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor
AxiosClient.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    return Promise.reject(error);
  }
);

export default AxiosClient;
