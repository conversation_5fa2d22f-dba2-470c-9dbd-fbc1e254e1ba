{"name": "template-2", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "axios": "^1.7.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "framer-motion": "^12.23.0", "lodash": "^4.17.21", "lottie-react": "^2.4.1", "lucide-react": "^0.525.0", "next": "15.3.5", "react": "^19.0.0", "react-day-picker": "^9.9.0", "react-dom": "^19.0.0", "react-google-recaptcha": "^3.1.0", "react-hook-form": "^7.62.0", "signature_pad": "^5.0.10", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/lodash": "^4.17.20", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-google-recaptcha": "^2.1.9", "eslint": "^9", "eslint-config-next": "15.3.5", "tailwindcss": "^4", "tw-animate-css": "^1.3.7", "typescript": "^5"}}